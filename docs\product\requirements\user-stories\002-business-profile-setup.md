# User Story: Business Profile Setup

- **ID:** 002-business-profile-setup
- **Title:** Set up business profile and company details
- **As a:** newly registered solo surveyor
- **I want to:** configure my business profile and company information
- **So that:** my reports and communications are professionally branded

## Acceptance Criteria

- [ ] User can enter company name and trading name
- [ ] User can upload company logo (with size/format validation)
- [ ] User can add business address and contact details
- [ ] User can enter professional qualifications (BOHS P402, RICS, etc.)
- [ ] User can select primary survey types (asbestos, legionella, fire safety, etc.)
- [ ] User can configure default report branding/footer
- [ ] Profile information is saved locally and synced when online
- [ ] User can edit profile information after initial setup
- [ ] Profile completion is tracked with progress indicator

## Dependencies

- 001-authentication-signup
- File upload service for logo handling
- Supabase storage configuration

## Notes

- Professional qualifications list should be pre-populated with UK standards
- Survey types should align with competitive analysis in user-functionality-analysis.md
- Logo upload should be optimized for mobile bandwidth