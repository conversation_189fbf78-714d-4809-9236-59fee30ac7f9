import { useCallback, useEffect, useState } from "react";
import { supabase } from "./supabaseClient";
export function useSupabaseAuth() {
    const [user, setUser] = useState(null);
    const [session, setSession] = useState(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    useEffect(() => {
        console.log("🔧 Supabase auth hook initializing...");
        supabase.auth
            .getSession()
            .then(({ data, error }) => {
            console.log("📡 Supabase getSession result:", {
                session: data.session ? "exists" : "null",
                user: data.session?.user?.email || "null",
                error: error?.message,
            });
            setSession(data.session ?? null);
            setUser(data.session?.user ?? null);
            setLoading(false);
            console.log("✅ Auth loading complete, user:", data.session?.user?.email || "null");
        })
            .catch((err) => {
            console.error("❌ Supabase getSession error:", err);
            setLoading(false);
        });
        const { data: listener } = supabase.auth.onAuthStateChange((_event, session) => {
            console.log("🔄 Auth state change:", {
                event: _event,
                user: session?.user?.email || "null",
            });
            setSession(session);
            setUser(session?.user ?? null);
        });
        return () => {
            listener.subscription.unsubscribe();
        };
    }, []);
    const signInWithEmail = useCallback(async (email, password) => {
        setLoading(true);
        setError(null);
        const { error } = await supabase.auth.signInWithPassword({
            email,
            password,
        });
        setError(error);
        setLoading(false);
        return error;
    }, []);
    const signInWithMagicLink = useCallback(async (email) => {
        setLoading(true);
        setError(null);
        const { error } = await supabase.auth.signInWithOtp({ email });
        setError(error);
        setLoading(false);
        return error;
    }, []);
    const signUp = useCallback(async (email, password) => {
        setLoading(true);
        setError(null);
        const { error } = await supabase.auth.signUp({
            email,
            password,
        });
        setError(error);
        setLoading(false);
        return error;
    }, []);
    const signOut = useCallback(async () => {
        setLoading(true);
        setError(null);
        const { error } = await supabase.auth.signOut();
        setError(error);
        setLoading(false);
        return error;
    }, []);
    return {
        user,
        session,
        loading,
        error,
        signInWithEmail,
        signInWithMagicLink,
        signUp,
        signOut,
    };
}
//# sourceMappingURL=useSupabaseAuth.js.map