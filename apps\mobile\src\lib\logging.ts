// Centralized logging utility for mobile app
// Provides configurable debug logging with multiple activation methods

type LogLevel = 'debug' | 'info' | 'warn' | 'error';
type LogCategory = 'auth' | 'api' | 'app' | 'ui' | 'general';

interface LogEntry {
  level: LogLevel;
  category: LogCategory;
  message: string;
  data?: any;
  timestamp: Date;
}

class Logger {
  private isDebugEnabled: boolean | null = null;

  /**
   * Check if debug mode is enabled using multiple methods:
   * 1. Environment variable (VITE_DEBUG=true)
   * 2. Development mode (NODE_ENV=development or Vite dev mode)
   * 3. Query string (?debug=true)
   * 4. Local storage (localStorage.debug=true)
   */
  private checkDebugMode(): boolean {
    if (this.isDebugEnabled !== null) {
      return this.isDebugEnabled;
    }

    // Check environment variables
    if (import.meta.env.VITE_DEBUG === 'true') {
      this.isDebugEnabled = true;
      return true;
    }

    // Check if in development mode
    if (import.meta.env.DEV) {
      this.isDebugEnabled = true;
      return true;
    }

    // Check query string (browser only)
    if (typeof window !== 'undefined') {
      const urlParams = new URLSearchParams(window.location.search);
      if (urlParams.has('debug') || urlParams.get('debug') === 'true') {
        this.isDebugEnabled = true;
        return true;
      }

      // Check local storage
      try {
        if (localStorage.getItem('debug') === 'true') {
          this.isDebugEnabled = true;
          return true;
        }
      } catch (e) {
        // localStorage might not be available
      }
    }

    this.isDebugEnabled = false;
    return false;
  }

  /**
   * Format log message with emoji prefix and category
   */
  private formatMessage(level: LogLevel, category: LogCategory, message: string): string {
    const emojis = {
      debug: '🔍',
      info: 'ℹ️',
      warn: '⚠️',
      error: '❌'
    };

    const categoryEmojis = {
      auth: '🔐',
      api: '🌐',
      app: '📱',
      ui: '🎨',
      general: '📝'
    };

    return `${emojis[level]} ${categoryEmojis[category]} [${category.toUpperCase()}] ${message}`;
  }

  /**
   * Internal logging method
   */
  private log(level: LogLevel, category: LogCategory, message: string, data?: any): void {
    if (!this.checkDebugMode()) {
      return;
    }

    const formattedMessage = this.formatMessage(level, category, message);
    const logEntry: LogEntry = {
      level,
      category,
      message,
      data,
      timestamp: new Date()
    };

    // Output to console based on level
    switch (level) {
      case 'debug':
        if (data !== undefined) {
          console.log(formattedMessage, data);
        } else {
          console.log(formattedMessage);
        }
        break;
      case 'info':
        if (data !== undefined) {
          console.info(formattedMessage, data);
        } else {
          console.info(formattedMessage);
        }
        break;
      case 'warn':
        if (data !== undefined) {
          console.warn(formattedMessage, data);
        } else {
          console.warn(formattedMessage);
        }
        break;
      case 'error':
        if (data !== undefined) {
          console.error(formattedMessage, data);
        } else {
          console.error(formattedMessage);
        }
        break;
    }

    // In the future, could also send to external logging service
    // this.sendToExternalLogger(logEntry);
  }

  // Public API methods
  debug(category: LogCategory, message: string, data?: any): void {
    this.log('debug', category, message, data);
  }

  info(category: LogCategory, message: string, data?: any): void {
    this.log('info', category, message, data);
  }

  warn(category: LogCategory, message: string, data?: any): void {
    this.log('warn', category, message, data);
  }

  error(category: LogCategory, message: string, data?: any): void {
    this.log('error', category, message, data);
  }

  // Convenience methods for common auth scenarios
  authDebug(message: string, data?: any): void {
    this.debug('auth', message, data);
  }

  authInfo(message: string, data?: any): void {
    this.info('auth', message, data);
  }

  authError(message: string, data?: any): void {
    this.error('auth', message, data);
  }

  // Convenience methods for API scenarios
  apiDebug(message: string, data?: any): void {
    this.debug('api', message, data);
  }

  apiError(message: string, data?: any): void {
    this.error('api', message, data);
  }

  // Convenience methods for app scenarios
  appDebug(message: string, data?: any): void {
    this.debug('app', message, data);
  }

  appInfo(message: string, data?: any): void {
    this.info('app', message, data);
  }

  // Force enable/disable debug mode (useful for testing)
  setDebugMode(enabled: boolean): void {
    this.isDebugEnabled = enabled;
  }

  // Check if debug mode is currently enabled
  isDebugModeEnabled(): boolean {
    return this.checkDebugMode();
  }
}

// Export singleton instance
export const logger = new Logger();

// Export convenience functions for common usage patterns
export const debugAuth = (message: string, data?: any) => logger.authDebug(message, data);
export const debugApi = (message: string, data?: any) => logger.apiDebug(message, data);
export const debugApp = (message: string, data?: any) => logger.appDebug(message, data);
export const debugUI = (message: string, data?: any) => logger.debug('ui', message, data);

// Export the Logger class for advanced usage
export { Logger };
export type { LogLevel, LogCategory };
