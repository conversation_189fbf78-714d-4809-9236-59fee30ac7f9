# Mobile App Utilities

## Logging System

The mobile app uses a centralized logging system that provides configurable debug output.

### Usage

```typescript
import { debugAuth, debugApi, debugApp, debugUI, logger } from './logging';

// Convenience functions for common categories
debugAuth('User signed in', { email: '<EMAIL>' });
debugApi('API request failed', { error: 'Network timeout' });
debugApp('Component mounted', { componentName: 'AuthPage' });
debugUI('Button clicked', { buttonId: 'submit' });

// Direct logger usage for custom categories
logger.debug('custom', 'Custom debug message', { data: 'value' });
logger.info('general', 'Info message');
logger.warn('api', 'Warning message');
logger.error('auth', 'Error message');
```

### Enabling Debug Mode

Debug logging can be enabled in multiple ways:

1. **Environment Variable** (recommended for development)
   ```bash
   VITE_DEBUG=true pnpm dev
   ```

2. **Development Mode** (automatic)
   - Logs are automatically enabled when running `pnpm dev`

3. **Query String** (convenient for testing)
   ```
   http://localhost:5173/?debug=true
   ```

4. **Local Storage** (persistent across sessions)
   ```javascript
   localStorage.setItem('debug', 'true');
   ```

5. **Programmatically** (for testing)
   ```typescript
   import { logger } from './logging';
   logger.setDebugMode(true);
   ```

### Log Categories

- `auth` 🔐 - Authentication related logs
- `api` 🌐 - API calls and responses
- `app` 📱 - Application lifecycle and routing
- `ui` 🎨 - User interface interactions
- `general` 📝 - General purpose logs

### Log Levels

- `debug` 🔍 - Detailed debugging information
- `info` ℹ️ - General information
- `warn` ⚠️ - Warning messages
- `error` ❌ - Error messages

### Production Behavior

In production builds:
- Logs are disabled by default
- Can be enabled with `?debug=true` query parameter
- No performance impact when disabled

## API Utilities

### useApi Hook

```typescript
import { useApi } from './api';

const MyComponent = () => {
  const api = useApi();
  
  const loadData = async () => {
    try {
      const sites = await api.getSites();
      console.log('Sites:', sites);
    } catch (error) {
      console.error('Failed to load sites:', error);
    }
  };
};
```
