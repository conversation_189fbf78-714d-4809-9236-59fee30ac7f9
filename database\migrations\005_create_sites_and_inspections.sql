-- Create sites table (adapted from tblSites)
-- Represents buildings/complexes containing multiple properties
CREATE TABLE sites (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    
    -- Multi-tenant
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    
    -- Relationships
    client_id UUID NOT NULL REFERENCES clients(id) ON DELETE CASCADE,
    
    -- Site details
    name TEXT NOT NULL,
    description TEXT,
    site_code TEXT,
    
    -- Address (could be normalized to separate table)
    address_line_1 TEXT,
    address_line_2 TEXT,
    city TEXT,
    state_province TEXT,
    postal_code TEXT,
    country TEXT DEFAULT 'UK',
    
    -- Coordinates for mapping
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),
    
    -- Site plans and documents
    site_plan_url TEXT,
    floor_plan_urls JSONB DEFAULT '[]',
    
    -- Status
    status TEXT DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'archived')),
    archived_at TIMESTAMPTZ,
    archived_by UUID REFERENCES auth.users(id),
    archive_reason TEXT
);

-- Create inspections table (adapted from tblInspections)
CREATE TABLE inspections (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    
    -- Multi-tenant
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    
    -- Relationships
    property_id UUID NOT NULL REFERENCES properties(id) ON DELETE CASCADE,
    assigned_inspector_id UUID REFERENCES auth.users(id),
    project_id UUID, -- Will be added later for project grouping
    
    -- Inspection details
    inspection_type TEXT NOT NULL,
    title TEXT NOT NULL,
    description TEXT,
    
    -- Scheduling
    scheduled_date DATE,
    due_date DATE,
    completed_at TIMESTAMPTZ,
    
    -- Status workflow
    status TEXT DEFAULT 'draft' CHECK (status IN (
        'draft', 'scheduled', 'in_progress', 'completed', 
        'requires_follow_up', 'approved', 'cancelled'
    )),
    
    -- Results and findings
    findings JSONB DEFAULT '{}',
    photos JSONB DEFAULT '[]',
    documents JSONB DEFAULT '[]',
    signature_data JSONB,
    
    -- Follow-up tracking
    follow_up_required BOOLEAN DEFAULT false,
    follow_up_date DATE,
    follow_up_notes TEXT,
    
    -- Approval workflow
    reviewed_by UUID REFERENCES auth.users(id),
    reviewed_at TIMESTAMPTZ,
    approved_by UUID REFERENCES auth.users(id),
    approved_at TIMESTAMPTZ
);

-- Add foreign key for sites to properties
ALTER TABLE properties 
ADD CONSTRAINT fk_properties_site_id 
FOREIGN KEY (site_id) REFERENCES sites(id);

-- RLS policies for sites
ALTER TABLE sites ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view sites in their tenant"
    ON sites FOR SELECT
    USING (tenant_id = (auth.jwt() ->> 'tenant_id')::uuid);

CREATE POLICY "Schedulers and admins can manage sites"
    ON sites FOR ALL
    USING (
        tenant_id = (auth.jwt() ->> 'tenant_id')::uuid 
        AND (auth.jwt() ->> 'role') IN ('TenantAdmin', 'Scheduler')
    );

-- RLS policies for inspections
ALTER TABLE inspections ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view inspections in their tenant"
    ON inspections FOR SELECT
    USING (tenant_id = (auth.jwt() ->> 'tenant_id')::uuid);

CREATE POLICY "Inspectors can view and update assigned inspections"
    ON inspections FOR ALL
    USING (
        tenant_id = (auth.jwt() ->> 'tenant_id')::uuid 
        AND (
            (auth.jwt() ->> 'role') IN ('TenantAdmin', 'Scheduler')
            OR (
                (auth.jwt() ->> 'role') = 'Inspector'
                AND assigned_inspector_id = auth.uid()
            )
        )
    );

-- Indexes for sites
CREATE INDEX idx_sites_tenant_id ON sites(tenant_id);
CREATE INDEX idx_sites_client_id ON sites(client_id);
CREATE INDEX idx_sites_name ON sites(name);
CREATE INDEX idx_sites_status ON sites(status);

-- Indexes for inspections
CREATE INDEX idx_inspections_tenant_id ON inspections(tenant_id);
CREATE INDEX idx_inspections_property_id ON inspections(property_id);
CREATE INDEX idx_inspections_assigned_inspector_id ON inspections(assigned_inspector_id);
CREATE INDEX idx_inspections_status ON inspections(status);
CREATE INDEX idx_inspections_scheduled_date ON inspections(scheduled_date);
CREATE INDEX idx_inspections_due_date ON inspections(due_date);
CREATE INDEX idx_inspections_inspection_type ON inspections(inspection_type);

-- Updated_at triggers
CREATE TRIGGER update_sites_updated_at 
    BEFORE UPDATE ON sites 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_inspections_updated_at 
    BEFORE UPDATE ON inspections 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column(); 