# User Story: Inspection Creation

- **ID:** 005-inspection-creation
- **Title:** Create new inspections directly in mobile app
- **As a:** solo surveyor
- **I want to:** create and configure new inspection jobs
- **So that:** I can start surveying immediately and manage my work efficiently

## Acceptance Criteria

- [ ] User can create inspection linked to existing client and site
- [ ] User can select inspection type (asbestos, legionella, fire safety, etc.)
- [ ] User can set inspection date and estimated duration
- [ ] User can add inspection-specific notes and requirements
- [ ] User can attach reference documents (previous reports, site plans)
- [ ] User can set inspection status (scheduled, in progress, completed)
- [ ] User can configure inspection template based on type
- [ ] User can add pre-survey risk assessment notes
- [ ] User can create inspection offline (queued for sync)
- [ ] User can duplicate similar inspections to save time
- [ ] User can view inspection overview with all details

## Dependencies

- 003-client-management
- 004-site-management
- Inspection templates for different survey types

## Notes

- Inspection types should align with specializations from user-functionality-analysis.md
- Templates should be pre-configured for UK compliance (HSG264, RICS standards)
- Offline creation essential for field work scenarios
- Foundation for the core inspection workflow