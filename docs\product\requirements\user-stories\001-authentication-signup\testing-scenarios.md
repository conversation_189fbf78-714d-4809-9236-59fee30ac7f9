# Testing Scenarios - Use Case 001: Authentication & Signup

> **Purpose**: Detailed testing scenarios for User Story 001  
> **Status**: Scenarios Defined - Implementation Required  
> **Last Updated**: 2025-01-27

## 🧪 Test Scenario Overview

Four comprehensive test scenarios cover all aspects of authentication and signup functionality, including edge cases and error conditions.

## 📋 Scenario 1: Happy Path Registration and Login

### Test Steps
1. **Navigation**: Open mobile app and navigate to signup screen
2. **Registration**: Complete signup form with valid data
3. **Email Verification**: Verify email sent and complete verification
4. **Login**: Login with registered credentials
5. **Profile**: Verify user profile and tenant creation
6. **Logout**: Test logout functionality

### Detailed Test Cases

#### 1.1 Signup Process
```typescript
test('User can complete signup process', async ({ page }) => {
  await page.goto('/signup');
  
  // Fill signup form
  await page.fill('#email', '<EMAIL>');
  await page.fill('#password', 'SecurePass123!');
  await page.fill('#confirmPassword', 'SecurePass123!');
  await page.fill('#businessName', 'Test Survey Business');
  await page.check('#terms');
  
  // Submit form
  await page.click('#signup-button');
  
  // Verify success message
  await expect(page.locator('#success-message')).toBeVisible();
  await expect(page.locator('#success-message')).toContainText('verification email sent');
});
```

#### 1.2 Email Verification
```typescript
test('Email verification completes successfully', async ({ page }) => {
  // Simulate email verification (mock email service)
  const verificationToken = 'mock-verification-token';
  
  await page.goto(`/verify-email?token=${verificationToken}`);
  
  // Verify success
  await expect(page.locator('#verification-success')).toBeVisible();
  await expect(page.url()).toContain('/welcome');
});
```

#### 1.3 Login Flow
```typescript
test('User can login with verified account', async ({ page }) => {
  await page.goto('/login');
  
  // Fill login form
  await page.fill('#email', '<EMAIL>');
  await page.fill('#password', 'SecurePass123!');
  
  // Submit form
  await page.click('#login-button');
  
  // Verify successful login
  await expect(page.url()).toContain('/dashboard');
  await expect(page.locator('#user-menu')).toBeVisible();
});
```

#### 1.4 Tenant Creation Validation
```typescript
test('Tenant is created automatically during signup', async ({ page }) => {
  // Login as test user
  await loginAsTestUser(page);
  
  // Navigate to tenant settings
  await page.goto('/settings/tenant');
  
  // Verify tenant exists
  await expect(page.locator('#tenant-name')).toContainText('Test Survey Business');
  await expect(page.locator('#user-role')).toContainText('TenantAdmin');
});
```

### Expected Results
- [ ] Signup form validates all fields correctly
- [ ] Email verification sent within 30 seconds
- [ ] User can complete email verification
- [ ] Login works with correct credentials
- [ ] Tenant created automatically with correct name
- [ ] User assigned TenantAdmin role
- [ ] Session persists across page reloads

## 📋 Scenario 2: Password Reset Flow

### Test Steps
1. **Forgot Password**: Initiate password reset from login screen
2. **Email Delivery**: Verify reset email sent
3. **Reset Link**: Click reset link and verify token
4. **New Password**: Set new password with validation
5. **Login**: Login with new password
6. **Security**: Verify old password no longer works

### Detailed Test Cases

#### 2.1 Password Reset Request
```typescript
test('User can request password reset', async ({ page }) => {
  await page.goto('/login');
  
  // Click forgot password link
  await page.click('#forgot-password');
  
  // Fill email
  await page.fill('#reset-email', '<EMAIL>');
  
  // Submit request
  await page.click('#reset-button');
  
  // Verify success message
  await expect(page.locator('#reset-success')).toBeVisible();
  await expect(page.locator('#reset-success')).toContainText('reset link sent');
});
```

#### 2.2 Password Reset Completion
```typescript
test('User can complete password reset', async ({ page }) => {
  const resetToken = 'mock-reset-token';
  
  await page.goto(`/reset-password?token=${resetToken}`);
  
  // Fill new password form
  await page.fill('#new-password', 'NewSecurePass123!');
  await page.fill('#confirm-password', 'NewSecurePass123!');
  
  // Submit form
  await page.click('#reset-submit');
  
  // Verify success
  await expect(page.locator('#reset-complete')).toBeVisible();
  await expect(page.url()).toContain('/login');
});
```

#### 2.3 Login with New Password
```typescript
test('User can login with new password', async ({ page }) => {
  await page.goto('/login');
  
  // Login with new password
  await page.fill('#email', '<EMAIL>');
  await page.fill('#password', 'NewSecurePass123!');
  await page.click('#login-button');
  
  // Verify success
  await expect(page.url()).toContain('/dashboard');
});
```

### Expected Results
- [ ] Password reset request sends email within 30 seconds
- [ ] Reset link expires after 1 hour
- [ ] New password meets security requirements
- [ ] Old password no longer works
- [ ] User can login with new password
- [ ] All existing sessions invalidated

## 📋 Scenario 3: Validation Error Handling

### Test Steps
1. **Form Validation**: Test all field validation rules
2. **Server Errors**: Test server-side error handling
3. **Network Errors**: Test network connectivity issues
4. **Security Errors**: Test rate limiting and security features
5. **Recovery**: Test error recovery and user guidance

### Detailed Test Cases

#### 3.1 Form Validation
```typescript
test('Signup form validates all fields', async ({ page }) => {
  await page.goto('/signup');
  
  // Test email validation
  await page.fill('#email', 'invalid-email');
  await page.blur('#email');
  await expect(page.locator('#email-error')).toContainText('valid email');
  
  // Test password strength
  await page.fill('#password', 'weak');
  await page.blur('#password');
  await expect(page.locator('#password-error')).toContainText('minimum 8 characters');
  
  // Test password match
  await page.fill('#password', 'SecurePass123!');
  await page.fill('#confirmPassword', 'DifferentPass123!');
  await page.blur('#confirmPassword');
  await expect(page.locator('#confirm-error')).toContainText('passwords must match');
});
```

#### 3.2 Server Error Handling
```typescript
test('Server errors are handled gracefully', async ({ page }) => {
  // Mock server error
  await page.route('**/auth/signup', route => 
    route.fulfill({ status: 500, body: 'Server Error' })
  );
  
  await page.goto('/signup');
  await fillValidSignupForm(page);
  await page.click('#signup-button');
  
  // Verify error message
  await expect(page.locator('#error-message')).toBeVisible();
  await expect(page.locator('#error-message')).toContainText('server error');
  await expect(page.locator('#retry-button')).toBeVisible();
});
```

#### 3.3 Network Error Handling
```typescript
test('Network errors are handled gracefully', async ({ page, context }) => {
  // Go offline
  await context.setOffline(true);
  
  await page.goto('/signup');
  await fillValidSignupForm(page);
  await page.click('#signup-button');
  
  // Verify offline handling
  await expect(page.locator('#offline-message')).toBeVisible();
  await expect(page.locator('#offline-queue')).toContainText('queued for sync');
  
  // Go online
  await context.setOffline(false);
  
  // Verify auto-sync
  await expect(page.locator('#sync-success')).toBeVisible();
});
```

### Expected Results
- [ ] All form fields validate correctly
- [ ] Clear, helpful error messages shown
- [ ] Server errors handled gracefully
- [ ] Network errors trigger offline mode
- [ ] Users can retry failed operations
- [ ] Error recovery guidance provided

## 📋 Scenario 4: Offline Registration Queuing

### Test Steps
1. **Offline Mode**: Simulate offline connectivity
2. **Offline Registration**: Complete signup while offline
3. **Queue Storage**: Verify registration queued locally
4. **Online Sync**: Restore connectivity and sync
5. **Conflict Resolution**: Handle sync conflicts
6. **User Feedback**: Verify user informed of status

### Detailed Test Cases

#### 4.1 Offline Registration
```typescript
test('Offline registration is queued', async ({ page, context }) => {
  // Go offline
  await context.setOffline(true);
  
  await page.goto('/signup');
  
  // Verify offline indicator
  await expect(page.locator('#offline-indicator')).toBeVisible();
  
  // Complete signup
  await fillValidSignupForm(page);
  await page.click('#signup-button');
  
  // Verify queued message
  await expect(page.locator('#offline-queue')).toBeVisible();
  await expect(page.locator('#offline-queue')).toContainText('queued for sync');
});
```

#### 4.2 Sync on Reconnection
```typescript
test('Queued registration syncs when online', async ({ page, context }) => {
  // Start offline with queued registration
  await context.setOffline(true);
  await queueOfflineRegistration(page);
  
  // Go online
  await context.setOffline(false);
  
  // Verify auto-sync
  await expect(page.locator('#sync-progress')).toBeVisible();
  await expect(page.locator('#sync-success')).toBeVisible();
  
  // Verify registration completed
  await expect(page.locator('#registration-complete')).toBeVisible();
});
```

#### 4.3 Conflict Resolution
```typescript
test('Email conflicts are handled during sync', async ({ page, context }) => {
  // Mock email conflict during sync
  await page.route('**/auth/signup', route => 
    route.fulfill({ 
      status: 409, 
      body: JSON.stringify({ error: 'Email already exists' })
    })
  );
  
  // Queue offline registration
  await context.setOffline(true);
  await queueOfflineRegistration(page);
  
  // Go online and sync
  await context.setOffline(false);
  
  // Verify conflict handling
  await expect(page.locator('#sync-conflict')).toBeVisible();
  await expect(page.locator('#sync-conflict')).toContainText('email already exists');
  await expect(page.locator('#resolve-conflict')).toBeVisible();
});
```

### Expected Results
- [ ] Offline mode clearly indicated to user
- [ ] Registration queued when offline
- [ ] Automatic sync when connectivity restored
- [ ] Sync progress indicated to user
- [ ] Conflicts handled gracefully
- [ ] User can resolve conflicts manually

## 🔧 Test Environment Setup

### Required Test Data
```typescript
// Test user data
const testUsers = [
  {
    email: '<EMAIL>',
    password: 'SecurePass123!',
    businessName: 'Test Survey Business'
  },
  {
    email: '<EMAIL>',
    password: 'ExistingPass123!',
    businessName: 'Existing Business'
  }
];

// Test tenant data
const testTenants = [
  {
    name: 'Test Survey Business',
    adminEmail: '<EMAIL>'
  }
];
```

### Mock Services
```typescript
// Email service mock
class MockEmailService {
  async sendVerificationEmail(email: string): Promise<void> {
    // Mock email sending
    console.log(`Verification email sent to ${email}`);
  }
  
  async sendPasswordResetEmail(email: string): Promise<void> {
    // Mock password reset email
    console.log(`Password reset email sent to ${email}`);
  }
}

// Database mock for testing
class MockDatabase {
  async createUser(userData: any): Promise<any> {
    // Mock user creation
    return { id: 'mock-user-id', ...userData };
  }
  
  async createTenant(tenantData: any): Promise<any> {
    // Mock tenant creation
    return { id: 'mock-tenant-id', ...tenantData };
  }
}
```

## 📊 Test Coverage Requirements

### Unit Tests (95% coverage target)
- Form validation logic
- Authentication state management
- Offline queue management
- Error handling functions
- Security validation

### Integration Tests (80% coverage target)
- Database operations
- Email service integration
- Authentication flows
- Multi-tenant isolation
- Sync mechanisms

### E2E Tests (100% scenario coverage)
- All 4 scenarios must pass
- Cross-browser compatibility
- Mobile device testing
- Network condition testing
- Performance validation

## 🎯 Acceptance Criteria

### Test Implementation
- [ ] All 4 scenarios implemented in Playwright
- [ ] Tests run reliably in CI/CD pipeline
- [ ] Test data setup and teardown automated
- [ ] Mock services properly configured
- [ ] Cross-browser testing configured

### Test Results
- [ ] All tests passing locally
- [ ] All tests passing in CI/CD
- [ ] Performance benchmarks met
- [ ] Security validation complete
- [ ] Manual testing checklist complete

### Documentation
- [ ] Test scenarios documented
- [ ] Test data requirements documented
- [ ] Test environment setup documented
- [ ] Troubleshooting guide created
- [ ] Coverage reports generated

---

**Next Steps**: Implement these scenarios in Playwright and integrate into CI/CD pipeline.