{"name": "@strata/api-bff", "version": "0.0.1", "private": true, "scripts": {"dev": "wrangler pages dev functions/", "deploy": "wrangler pages deploy functions/", "lint": "eslint functions/", "test": "vitest run"}, "dependencies": {"@strata/auth": "workspace:*", "@strata/core": "workspace:*", "@supabase/supabase-js": "^2.39.0", "hono": "^4.0.5"}, "devDependencies": {"@cloudflare/workers-types": "^4.20240208.0", "@types/node": "^20.11.19", "eslint": "^8.56.0", "typescript": "^5.3.3", "vitest": "^1.2.2", "wrangler": "^3.28.1"}}