// Simple test script to verify the API endpoint works
// Using built-in fetch (Node.js 18+)

async function testSitesEndpoint() {
  try {
    console.log("Testing sites API endpoint...");

    // First test the health check
    const healthResponse = await fetch("http://localhost:8787/");
    const healthData = await healthResponse.json();
    console.log("Health check:", healthData);

    // Test the sites endpoint (this should fail without auth)
    const sitesResponse = await fetch("http://localhost:8787/api/sites");
    const sitesData = await sitesResponse.json();
    console.log("Sites endpoint (no auth):", sitesData);
  } catch (error) {
    console.error("Error testing API:", error.message);
  }
}

testSitesEndpoint();
