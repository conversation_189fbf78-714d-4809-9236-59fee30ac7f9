{"name": "@strata/ui", "version": "0.0.1", "private": true, "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "scripts": {"build": "tsup src/index.ts", "dev": "tsup src/index.ts --watch", "lint": "eslint src/ test/", "test": "vitest run"}, "dependencies": {"@emotion/react": "^11.11.3", "@emotion/styled": "^11.11.0", "@mui/material": "^5.15.10"}, "devDependencies": {"@types/node": "^20.11.19", "@types/react": "^18.2.55", "@types/react-dom": "^18.2.19", "eslint": "^8.56.0", "tsup": "^8.0.2", "typescript": "^5.3.3", "vitest": "^1.2.2"}, "peerDependencies": {"react": "^18.2.0", "react-dom": "^18.2.0"}}