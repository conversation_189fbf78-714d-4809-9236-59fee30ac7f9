# Task 002-001: Create Business Profile Data Model

**Parent Use Case:** 002-business-profile-setup  
**Task ID:** 002-001  
**Title:** Create Business Profile Data Model  
**Estimated Development Time:** 1-2 hours (including tests/docs)  
**Estimated Agent Time:** 2-3 minutes

## Description

Define and implement the business profile data structure in Supabase and create corresponding TypeScript interfaces. This forms the foundation for all business profile functionality, establishing the database schema, RLS policies, and type definitions that other components will use.

## Acceptance Criteria

- [x] Business profile table exists in Supabase with all required fields
- [x] RLS policies are implemented for multi-tenant data isolation
- [x] TypeScript interfaces are defined and exported
- [x] Database migration scripts are created and documented
- [x] Table relationships are properly defined with foreign keys
- [x] Data validation rules are implemented at the database level
- [x] Unit tests validate data model constraints
- [x] Documentation explains the data model structure and relationships

## Deliverables Checklist

### Database Schema

- [x] Create `business_profiles` table in Supabase
- [x] Add required columns: id, tenant_id, user_id, company_name, trading_name, etc.
- [x] Implement proper data types and constraints
- [x] Add created_at, updated_at timestamps
- [x] Create indexes for performance optimization

### Security & Access Control

- [x] Implement RLS policies for tenant isolation
- [x] Create policy for users to read their own business profile
- [x] Create policy for users to update their own business profile
- [x] Test RLS policies with different user scenarios

### TypeScript Interfaces

- [x] Define BusinessProfile interface with all fields
- [x] Define CreateBusinessProfileRequest interface
- [x] Define UpdateBusinessProfileRequest interface
- [x] Export interfaces from shared types package

### Migration & Documentation

- [x] Create database migration script
- [x] Create rollback migration script
- [x] Document table structure and field purposes
- [x] Document RLS policy logic and security model

### Testing

- [x] Write unit tests for TypeScript interface validation
- [x] Test database constraints and validation rules
- [x] Test RLS policies with multiple tenants
- [x] Verify migration scripts work correctly

## Dependencies

- **Required Before Starting:**

  - 001-authentication-signup (user authentication system must exist)
  - Supabase project setup and configuration
  - Multi-tenant architecture foundation

- **Blocks These Tasks:**
  - 002-002 (Business Profile Form Component)
  - 002-004 (Logo Upload Component)
  - 002-008 (Business Profile API Endpoints)

## Technical Considerations

### Database Design

- Must include `tenant_id` for multi-tenancy support
- Consider future expansion fields (e.g., Companies House number)
- Optimize for mobile app query patterns
- Plan for offline sync requirements

### Security

- RLS policies must prevent cross-tenant data access
- Consider audit trail requirements for business data
- Validate all user inputs at database level

### Performance

- Index frequently queried fields (tenant_id, user_id)
- Consider read replica requirements for reporting
- Plan for horizontal scaling if needed

### UK Business Requirements

- Consider future integration with Companies House API
- Plan for UK-specific business data (VAT number, etc.)
- Consider professional body registration numbers

## Definition of Done

- [x] Database table created and accessible via Supabase ✅ VERIFIED
- [x] RLS policies tested and verified secure ✅ VERIFIED
- [x] TypeScript interfaces compile without errors ✅ VERIFIED
- [x] Migration scripts tested in development environment ✅ VERIFIED
- [x] All unit tests pass ✅ VERIFIED (29/29 tests passing)
- [x] Code review completed ✅ VERIFIED
- [x] Documentation updated and reviewed ✅ VERIFIED
- [x] Security review completed for RLS policies ✅ VERIFIED

## Notes

- This is a foundational task - take extra care with the data model design
- Consider future requirements but don't over-engineer
- Ensure the schema supports both online and offline usage patterns
- The business profile is central to report generation and branding

## ✅ TASK COMPLETED

**Completion Date:** 2024-12-17
**Status:** COMPLETE - All acceptance criteria met and verified

### Deliverables Created:

- ✅ Database table: `business_profiles` created in Supabase
- ✅ Migration scripts: `006_create_business_profiles.sql` and rollback
- ✅ TypeScript interfaces: BusinessProfile, CreateBusinessProfileRequest, UpdateBusinessProfileRequest
- ✅ Unit tests: 29 tests passing (types and validation)
- ✅ Documentation: Complete data model documentation
- ✅ RLS policies: Multi-tenant security implemented and verified

### Verification Results:

- ✅ Table accessible via Supabase client
- ✅ Foreign key constraints working
- ✅ Email validation constraints working
- ✅ RLS policies preventing unauthorized access
- ✅ All TypeScript interfaces compile without errors

**Ready for next tasks:** 002-002 (Business Profile Form), 002-004 (Logo Upload), 002-008 (API Endpoints)
