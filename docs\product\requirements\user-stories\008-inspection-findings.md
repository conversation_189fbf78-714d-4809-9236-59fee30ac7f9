# User Story: Inspection Findings Recording

- **ID:** 008-inspection-findings
- **Title:** Record detailed findings and observations during inspections
- **As a:** solo surveyor conducting field inspections
- **I want to:** systematically record findings, risks, and observations
- **So that:** I can generate compliant reports with proper risk assessments

## Acceptance Criteria

- [ ] User can add findings with detailed descriptions
- [ ] User can assign risk scores following UK standards (HSG264 2-12 scale)
- [ ] User can categorize findings by type (hazard, defect, recommendation)
- [ ] User can link findings to specific rooms or areas
- [ ] User can attach photos and media to findings
- [ ] User can record location/accessibility status (inspected/inaccessible)
- [ ] User can add recommendations and remedial actions
- [ ] User can mark findings as requiring follow-up
- [ ] User can record sample information and laboratory details
- [ ] User can save findings offline and sync when connected
- [ ] User can duplicate similar findings to save time

## Dependencies

- 007-photo-media-capture
- 006-offline-inspection-workflow
- UK compliance scoring templates (HSG264, RICS standards)

## Notes

- Risk scoring must comply with HSG264 standards from user-functionality-analysis.md
- "Assumption of suspicion" handling for inaccessible areas
- Sample tracking essential for UKAS laboratory coordination
- Foundation for professional report generation