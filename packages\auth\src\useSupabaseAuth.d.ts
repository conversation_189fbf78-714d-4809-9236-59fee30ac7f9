import type { AuthError, Session, User } from "@supabase/supabase-js";
export declare function useSupabaseAuth(): {
    user: User | null;
    session: Session | null;
    loading: boolean;
    error: AuthError | null;
    signInWithEmail: (email: string, password: string) => Promise<AuthError | null>;
    signInWithMagicLink: (email: string) => Promise<AuthError | null>;
    signUp: (email: string, password: string) => Promise<AuthError | null>;
    signOut: () => Promise<AuthError | null>;
};
//# sourceMappingURL=useSupabaseAuth.d.ts.map