{"name": "@strata/desktop", "version": "0.0.1", "private": true, "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint src/", "preview": "vite preview", "test": "vitest run"}, "dependencies": {"@emotion/react": "^11.11.3", "@emotion/styled": "^11.11.0", "@fullcalendar/core": "^6.1.10", "@fullcalendar/daygrid": "^6.1.10", "@fullcalendar/interaction": "^6.1.10", "@fullcalendar/react": "^6.1.10", "@mui/material": "^5.15.10", "@strata/auth": "workspace:*", "@strata/core": "workspace:*", "@strata/ui": "workspace:*", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.22.1"}, "devDependencies": {"@types/react": "^18.2.55", "@types/react-dom": "^18.2.19", "@vitejs/plugin-react": "^4.2.1", "eslint": "^8.56.0", "typescript": "^5.3.3", "vite": "^5.1.3", "vitest": "^1.2.2"}}