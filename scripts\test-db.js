// Simple database connection test
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = 'https://fwktrittbrmqarkipcpz.supabase.co'
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ3a3RyaXR0YnJtcWFya2lwY3B6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAxODkxOTUsImV4cCI6MjA2NTc2NTE5NX0.cbfaZXV4NwBwRXVSRF0xSzWVUTmfXlwZa4ptuOF0se0'

const supabase = createClient(supabaseUrl, supabaseKey)

async function testConnection() {
  console.log('🔄 Testing Supabase connection...')
  
  try {
    // Test basic connection
    const { data, error } = await supabase.from('tenants').select('count').limit(1)
    
    if (error) {
      if (error.message.includes('relation "public.tenants" does not exist')) {
        console.log('⚠️  Tables not created yet. Please run migrations first.')
        console.log('📋 Next steps:')
        console.log('1. Go to https://fwktrittbrmqarkipcpz.supabase.co')
        console.log('2. Navigate to SQL Editor')
        console.log('3. Run migration files from database/migrations/ in order')
        return
      }
      throw error
    }
    
    console.log('✅ Database connection successful!')
    console.log('✅ Tables exist and are accessible')
    
    // Test basic query
    const { data: tenants, error: tenantsError } = await supabase
      .from('tenants')
      .select('*')
      .limit(5)
    
    if (tenantsError) {
      console.error('❌ Error querying tenants:', tenantsError)
      return
    }
    
    console.log(`📊 Found ${tenants.length} tenants`)
    if (tenants.length > 0) {
      console.log('Sample tenant:', tenants[0])
    }
    
  } catch (err) {
    console.error('❌ Connection failed:', err.message)
  }
}

testConnection() 