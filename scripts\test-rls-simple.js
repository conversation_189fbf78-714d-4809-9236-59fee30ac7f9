#!/usr/bin/env node

/**
 * Simple RLS Test Script
 * Tests if RLS policies are working correctly
 */

import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://fwktrittbrmqarkipcpz.supabase.co';
const anonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ3a3RyaXR0YnJtcWFya2lwY3B6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAxODkxOTUsImV4cCI6MjA2NTc2NTE5NX0.cbfaZXV4NwBwRXVSRF0xSzWVUTmfXlwZa4ptuOF0se0';
const serviceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ3a3RyaXR0YnJtcWFya2lwY3B6Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MDE4OTE5NSwiZXhwIjoyMDY1NzY1MTk1fQ.ldI65o8yJCY76i2LRi8eZA_ljv2peuomIyA9sucAg0M';

const anonClient = createClient(supabaseUrl, anonKey);
const serviceClient = createClient(supabaseUrl, serviceKey);

async function testRLSEnforcement() {
  console.log('🧪 Testing RLS Enforcement...');
  
  const tables = ['tenants', 'tenant_users', 'clients', 'properties', 'sites', 'inspections'];
  
  for (const table of tables) {
    try {
      const { data, error } = await anonClient.from(table).select('*').limit(1);
      
      if (error) {
        if (error.message.includes('RLS') || 
            error.message.includes('policy') || 
            error.message.includes('permission') ||
            error.message.includes('denied')) {
          console.log(`   ✅ ${table}: RLS working (${error.message})`);
        } else {
          console.log(`   ⚠️  ${table}: Unexpected error (${error.message})`);
        }
      } else {
        console.log(`   ❌ ${table}: RLS not enforced (returned ${data?.length || 0} rows)`);
      }
    } catch (e) {
      console.log(`   ✅ ${table}: RLS working (access denied)`);
    }
  }
}

async function checkRLSStatus() {
  console.log('\n📊 Checking RLS Status...');
  
  try {
    // Try to call our custom function if it exists
    const { data, error } = await serviceClient.rpc('check_rls_status');
    
    if (error) {
      console.log(`   ⚠️  Custom RLS check function not available: ${error.message}`);
      console.log('   💡 Please run database/rls-only-setup.sql first');
    } else {
      console.log('   📋 RLS Status:');
      data?.forEach(table => {
        console.log(`      ${table.table_name}: RLS ${table.rls_enabled ? '✅ Enabled' : '❌ Disabled'}, Policies: ${table.policy_count}`);
      });
    }
  } catch (e) {
    console.log(`   ⚠️  Error checking RLS status: ${e.message}`);
  }
}

async function testUserManagementFunctions() {
  console.log('\n👤 Testing User Management Functions...');
  
  try {
    // Test get_user_tenant_info function
    const { data, error } = await serviceClient.rpc('get_user_tenant_info', {
      user_id: '00000000-0000-0000-0000-000000000000' // dummy UUID
    });
    
    if (error) {
      console.log(`   ⚠️  get_user_tenant_info function not available: ${error.message}`);
    } else {
      console.log(`   ✅ get_user_tenant_info function working`);
    }
  } catch (e) {
    console.log(`   ⚠️  User management functions not available: ${e.message}`);
  }
}

async function showSampleData() {
  console.log('\n📋 Sample Data (via service role):');
  
  try {
    // Show tenants
    const { data: tenants, error: tenantsError } = await serviceClient
      .from('tenants')
      .select('id, name, slug')
      .limit(3);
      
    if (tenantsError) {
      console.log(`   ❌ Error fetching tenants: ${tenantsError.message}`);
    } else {
      console.log(`   📊 Tenants (${tenants?.length || 0}):`);
      tenants?.forEach(tenant => {
        console.log(`      - ${tenant.name} (${tenant.slug}) - ID: ${tenant.id}`);
      });
    }
    
    // Show tenant users
    const { data: users, error: usersError } = await serviceClient
      .from('tenant_users')
      .select('tenant_id, role, first_name, last_name')
      .limit(3);
      
    if (usersError) {
      console.log(`   ❌ Error fetching tenant users: ${usersError.message}`);
    } else {
      console.log(`   👥 Tenant Users (${users?.length || 0}):`);
      users?.forEach(user => {
        console.log(`      - ${user.first_name} ${user.last_name} (${user.role})`);
      });
    }
    
  } catch (e) {
    console.log(`   ❌ Error fetching sample data: ${e.message}`);
  }
}

async function showSetupInstructions() {
  console.log('\n📋 Setup Instructions');
  console.log('=====================');
  
  console.log('\n1. **Configure RLS Policies**:');
  console.log('   - Go to Supabase SQL Editor: https://fwktrittbrmqarkipcpz.supabase.co');
  console.log('   - Run the contents of: database/rls-only-setup.sql');
  console.log('   - This will enable RLS and create all policies');
  
  console.log('\n2. **Configure JWT Claims** (Manual):');
  console.log('   - Go to Authentication > Hooks in Supabase Dashboard');
  console.log('   - Create a custom access token hook');
  console.log('   - Add tenant_id and role claims from tenant_users table');
  
  console.log('\n3. **Test Authentication Flow**:');
  console.log('   - Sign up a user through your app');
  console.log('   - Add user to tenant_users table using add_user_to_tenant()');
  console.log('   - Sign in and verify JWT contains tenant_id and role');
  
  console.log('\n4. **Verify RLS is Working**:');
  console.log('   - Run this script again after setup');
  console.log('   - All tables should show "RLS working"');
  console.log('   - Users should only see their tenant data');
}

async function main() {
  console.log('🔍 Strata Compliance RLS Test');
  console.log('==============================\n');
  
  // Test RLS enforcement
  await testRLSEnforcement();
  
  // Check RLS status
  await checkRLSStatus();
  
  // Test user management functions
  await testUserManagementFunctions();
  
  // Show sample data
  await showSampleData();
  
  // Show setup instructions
  await showSetupInstructions();
  
  console.log('\n🎯 Summary:');
  console.log('- If RLS shows "not enforced", run database/rls-only-setup.sql');
  console.log('- JWT claims still need manual configuration in Supabase Dashboard');
  console.log('- Test with actual user signup after configuration');
}

main().catch(console.error);
