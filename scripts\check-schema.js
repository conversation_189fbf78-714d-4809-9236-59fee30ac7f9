#!/usr/bin/env node

import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://fwktrittbrmqarkipcpz.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ3a3RyaXR0YnJtcWFya2lwY3B6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAxODkxOTUsImV4cCI6MjA2NTc2NTE5NX0.cbfaZXV4NwBwRXVSRF0xSzWVUTmfXlwZa4ptuOF0se0';

const supabase = createClient(supabaseUrl, supabaseKey);

async function checkSchema() {
  console.log('🔍 Checking database schema...\n');
  
  const tableNames = ['tenants', 'tenant_users', 'clients', 'properties', 'sites', 'inspections'];
  
  for (const table of tableNames) {
    try {
      const { data, error } = await supabase.from(table).select('*').limit(1);
      if (error) {
        console.log(`❌ Table '${table}': ${error.message}`);
      } else {
        console.log(`✅ Table '${table}': exists and accessible`);
        if (data && data.length > 0) {
          console.log(`   📊 Sample columns: ${Object.keys(data[0]).join(', ')}`);
        }
      }
    } catch (e) {
      console.log(`❌ Table '${table}': ${e.message}`);
    }
  }
  
  // Check RLS policies
  console.log('\n🔒 Checking Row Level Security...');
  try {
    // Try to access tenants without auth (should fail if RLS is working)
    const { data, error } = await supabase.from('tenants').select('*');
    if (error) {
      console.log('✅ RLS is active (access denied without auth)');
    } else {
      console.log('⚠️  RLS might not be configured (access allowed without auth)');
    }
  } catch (e) {
    console.log('✅ RLS is active (access denied without auth)');
  }
}

checkSchema().catch(console.error);
