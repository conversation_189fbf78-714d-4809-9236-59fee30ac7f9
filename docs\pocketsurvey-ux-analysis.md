# PocketSurvey – Screen-by-Screen UX Analysis

## Introduction

This document provides a detailed, screen-by-screen user experience (UX) analysis of PocketSurvey, based on public website materials, screenshots, and inferred workflows. The goal is to understand the app’s navigation, layout, and user flows in enough detail to inform the design of a competitive product.

---

## Outline

1. Onboarding & Account Creation
2. Buildings List Screen
3. Building Details Screen
4. Items List Screen
5. Item Details Screen
6. Jobs Calendar
7. Building Maps
8. Job Progress
9. Changing User/Template Options
10. Custom Survey Template Design
11. Report Generation
12. Help & Support

---

## 1. Onboarding & Account Creation

- **Entry Point:** Users download the app or access via desktop; greeted with a welcome/login screen.
- **Sign Up:** Email/password registration, possibly with organization or team code.
- **Guided Tour:** Likely a brief walkthrough of main features and template selection.
- **First Template Prompt:** Users are encouraged to select a survey template and create their first job/building.

## 2. Buildings List Screen

- **Overview:** Displays building addresses, organized by town, with main photo thumbnails.
- **Quick Actions:** Icons to create or view reports as PDFs.
- **Navigation:** Bottom icons for main app areas; top menu for advanced sections (calendar, progress, clients, users, app design, help).

## 3. Building Details Screen

- **Photo & Details:** Front-page photo with scrollable inspection details.
- **Action Icons:** Add items, create/open reports, email or save reports.
- **Add Items:** Add new inspection items for the building.

## 4. Items List Screen

- **List View:** Shows all items assessed during inspection, with thumbnails and descriptions.
- **Location & Status:** Item location and color-coded status visible.

## 5. Item Details Screen

- **Photo Overlay:** Item photo with description and status overlay.
- **Actions:** Add another item, copy item to other buildings.

## 6. Jobs Calendar

- **Calendar View:** Plan and view inspections by month, week, or day; color-coded by surveyor.
- **Navigation:** Arrows to move through dates; daily view shows inspection times.

## 7. Building Maps

- **Map Dashboard:** Preview locations of all buildings being inspected; red pins for locations.
- **Search & Filter:** Free format search to filter buildings on the map.

## 8. Job Progress

- **Progress Overview:** Status overview (scheduled, started, pending, completed) with color coding and counts.
- **By Date:** Buildings organized by inspection date, color-coded by status.

## 9. Changing User/Template Options

- **Feature Toggles:** Turn features on/off (e.g., jobs, sites, priority assessments) to suit workflow.
- **Report Fields:** Customize company info, report titles, and branding.

## 10. Custom Survey Template Design

- **App Design:** Configure lists of building elements, descriptions, features, defects, and recommendations.
- **Drop-Downs:** Custom drop-downs for inspection data entry.

## 11. Report Generation

- **Instant Reports:** Generate PDF reports on-site or from desktop.
- **Preview & Share:** Open, save, or email reports directly from the app.
- **Custom Branding:** Company logo and details included in reports.

## 12. Help & Support

- **In-App Help:** Access help topics, FAQs, and troubleshooting directly in the app.
- **Support Access:** Contact support team for assistance.

---

## Notes on Onboarding

- The onboarding flow is designed to get users started quickly with a template and first job.
- Guided tour and help resources are available early in the experience.
- Customization options are surfaced as users progress.

---

## Next Steps

- Further detail can be added as more information becomes available (e.g., screenshots, user videos, or direct app access).
- A full onboarding flow map can be created if/when the actual app flow is observed.
