#!/usr/bin/env node

import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://fwktrittbrmqarkipcpz.supabase.co';
const serviceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ3a3RyaXR0YnJtcWFya2lwY3B6Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MDE4OTE5NSwiZXhwIjoyMDY1NzY1MTk1fQ.ldI65o8yJCY76i2LRi8eZA_ljv2peuomIyA9sucAg0M';

const supabase = createClient(supabaseUrl, serviceKey);

async function checkRLS() {
  console.log('🔒 Checking Row Level Security status...\n');
  
  try {
    // Check if RLS is enabled on tables
    const { data: rlsStatus, error } = await supabase.rpc('check_rls_status');
    
    if (error) {
      console.log('Using direct SQL query to check RLS...');
      
      // Check RLS status using direct SQL
      const tables = ['tenants', 'tenant_users', 'clients', 'properties', 'sites', 'inspections'];
      
      for (const table of tables) {
        try {
          const { data, error } = await supabase
            .from('pg_class')
            .select('relname, relrowsecurity')
            .eq('relname', table)
            .single();
            
          if (data) {
            console.log(`Table '${table}': RLS ${data.relrowsecurity ? 'ENABLED' : 'DISABLED'}`);
          }
        } catch (e) {
          // Try alternative approach
          const { data: policies, error: policiesError } = await supabase
            .rpc('get_policies_for_table', { table_name: table });
            
          if (policiesError) {
            console.log(`❓ Table '${table}': Cannot determine RLS status`);
          } else {
            console.log(`Table '${table}': ${policies?.length || 0} policies found`);
          }
        }
      }
    }
    
    // Test actual RLS enforcement
    console.log('\n🧪 Testing RLS enforcement...');
    
    // Create anon client (no auth)
    const anonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ3a3RyaXR0YnJtcWFya2lwY3B6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAxODkxOTUsImV4cCI6MjA2NTc2NTE5NX0.cbfaZXV4NwBwRXVSRF0xSzWVUTmfXlwZa4ptuOF0se0';
    const anonClient = createClient(supabaseUrl, anonKey);
    
    // Test access to tenants table without auth
    const { data: tenantsData, error: tenantsError } = await anonClient
      .from('tenants')
      .select('*')
      .limit(1);
      
    if (tenantsError) {
      console.log('✅ RLS working: Anonymous access to tenants denied');
      console.log(`   Error: ${tenantsError.message}`);
    } else {
      console.log('⚠️  RLS issue: Anonymous access to tenants allowed');
      console.log(`   Returned ${tenantsData?.length || 0} rows`);
    }
    
  } catch (e) {
    console.error('Error checking RLS:', e.message);
  }
}

checkRLS().catch(console.error);
