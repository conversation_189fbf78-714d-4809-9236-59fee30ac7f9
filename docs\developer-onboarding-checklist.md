# Developer Onboarding Checklist

Complete this checklist to get fully set up for Strata Compliance development.

## 📋 Pre-Setup Checklist

### Required Accounts
- [ ] **GitHub Account**: Access to repository
- [ ] **Supabase Account**: Database and authentication
- [ ] **Cloudflare Account**: API hosting and deployment

### Required Software
- [ ] **Node.js 18+**: `node --version` shows ≥18.0.0
- [ ] **pnpm 8.15.4+**: `pnpm --version` shows ≥8.15.4
- [ ] **Git**: `git --version` works
- [ ] **VS Code**: Recommended editor with extensions

## 🚀 Setup Process

### 1. Repository Setup
- [ ] Clone repository: `git clone <repository-url>`
- [ ] Navigate to project: `cd strata-compliance`
- [ ] Install dependencies: `pnpm install`
- [ ] Verify build works: `pnpm build`

### 2. Environment Configuration
- [ ] Copy `apps/mobile/.env.example` to `apps/mobile/.env.local`
- [ ] Copy `apps/desktop/.env.example` to `apps/desktop/.env.local`
- [ ] Copy `apps/api-bff/.env.example` to `apps/api-bff/.env.local`
- [ ] Fill in Supabase credentials in all `.env.local` files
- [ ] Test environment: `pnpm dev` starts all apps

### 3. Database Setup
- [ ] Create Supabase project
- [ ] Run database migrations (see `database/SETUP.md`)
- [ ] Create initial tenant and admin user
- [ ] Test database connection: `node scripts/test-db.js`

### 4. VS Code Setup
- [ ] Install recommended extensions (VS Code will prompt)
- [ ] Verify TypeScript IntelliSense works
- [ ] Test debugging: Run "All Apps (Development)" configuration
- [ ] Verify formatting on save works

## 🧪 Verification Tests

### Build System
- [ ] `pnpm build` completes successfully
- [ ] `pnpm test` runs all tests
- [ ] `pnpm lint` shows no errors
- [ ] `pnpm format` formats code correctly

### Development Servers
- [ ] Mobile app runs on http://localhost:5173
- [ ] Desktop app runs on http://localhost:5174
- [ ] API BFF runs on http://localhost:8787
- [ ] Hot reload works when editing files

### Authentication Flow
- [ ] Can sign up new user in mobile app
- [ ] Can sign in existing user in desktop app
- [ ] JWT tokens contain tenant_id and role claims
- [ ] RLS policies prevent cross-tenant data access

### Database Operations
- [ ] Can create new client record
- [ ] Can create new site for client
- [ ] Can create new inspection for site
- [ ] Data is properly isolated by tenant

## 📚 Knowledge Areas

### Project Understanding
- [ ] Read `docs/README.md` for project overview
- [ ] Review `docs/project-plan-and-cursor-rules.md` for context
- [ ] Understand `docs/technical-architecture-plan.md`
- [ ] Check `docs/implementation-checklist.md` for current progress

### Code Architecture
- [ ] Understand monorepo structure (`apps/` and `packages/`)
- [ ] Know how to import between packages (`@strata/*`)
- [ ] Understand shared TypeScript configuration
- [ ] Know Turbo commands for building and testing

### Domain Knowledge
- [ ] Understand multi-tenant architecture
- [ ] Know the difference between Basic and Pro tiers
- [ ] Understand inspection workflow (mobile → desktop → reports)
- [ ] Know client portal vs internal tools distinction

### Development Workflow
- [ ] Know how to run specific package tests
- [ ] Understand how to debug applications
- [ ] Know how to add new dependencies
- [ ] Understand commit message conventions

## 🛠️ Common Development Tasks

### Adding New Features
- [ ] Know how to create new React components
- [ ] Understand how to add new database tables
- [ ] Know how to create new API endpoints
- [ ] Understand how to write tests for new features

### Working with Database
- [ ] Know how to create new migrations
- [ ] Understand RLS policy patterns
- [ ] Know how to use DatabaseService methods
- [ ] Understand tenant isolation requirements

### Mobile Development
- [ ] Know Ionic React component patterns
- [ ] Understand offline data handling
- [ ] Know how to test on mobile devices
- [ ] Understand Capacitor plugin usage

### Desktop Development
- [ ] Know Material-UI component patterns
- [ ] Understand report generation workflow
- [ ] Know how to create complex forms
- [ ] Understand client portal features

## 🚨 Troubleshooting

### Common Issues
- [ ] Know how to clear node_modules and reinstall
- [ ] Understand how to fix TypeScript errors
- [ ] Know how to debug database connection issues
- [ ] Understand how to resolve port conflicts

### Getting Help
- [ ] Know where to find documentation
- [ ] Understand how to check implementation progress
- [ ] Know how to test database setup
- [ ] Understand project organization principles

## ✅ Final Verification

### Ready for Development
- [ ] Can start all applications with one command
- [ ] Can run tests and see them pass
- [ ] Can make a small change and see it reflected
- [ ] Can debug applications in VS Code
- [ ] Understand the project structure and goals

### Team Integration
- [ ] Know how to create feature branches
- [ ] Understand code review process
- [ ] Know commit message conventions
- [ ] Understand deployment process

## 📞 Support

If you encounter issues during setup:

1. **Check documentation**: Start with `docs/development-setup.md`
2. **Review troubleshooting**: See common issues section above
3. **Test database**: Run `node scripts/test-db.js`
4. **Verify environment**: Check all `.env.local` files have correct values
5. **Ask for help**: Contact team lead with specific error messages

## 🎯 Next Steps

After completing this checklist:

1. **Pick a task**: Check `docs/implementation-checklist.md` for current priorities
2. **Start small**: Begin with a simple bug fix or test improvement
3. **Learn the domain**: Understand the compliance inspection workflow
4. **Contribute**: Make your first pull request

Welcome to the Strata Compliance development team! 🎉
