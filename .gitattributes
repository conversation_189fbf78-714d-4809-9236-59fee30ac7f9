# Auto detect text files and perform LF normalization
* text=auto

# Source code
*.js text eol=lf
*.jsx text eol=lf
*.ts text eol=lf
*.tsx text eol=lf
*.json text eol=lf
*.md text eol=lf
*.yml text eol=lf
*.yaml text eol=lf
*.sql text eol=lf
*.html text eol=lf
*.css text eol=lf
*.scss text eol=lf

# Config files
*.config.js text eol=lf
*.config.ts text eol=lf
.eslintrc.* text eol=lf
.prettierrc.* text eol=lf
tsconfig*.json text eol=lf
package.json text eol=lf
pnpm-lock.yaml text eol=lf

# Documentation
*.txt text eol=lf
LICENSE text eol=lf
README* text eol=lf

# Images
*.png binary
*.jpg binary
*.jpeg binary
*.gif binary
*.ico binary
*.svg text eol=lf

# Fonts
*.woff binary
*.woff2 binary
*.eot binary
*.ttf binary
*.otf binary
