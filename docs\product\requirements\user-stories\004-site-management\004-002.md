# Task 004-002: Create Core Site Form Component

**Parent Use Case:** 004-site-management  
**Task ID:** 004-002  
**Title:** Create Core Site Form Component  
**Estimated Development Time:** 3-4 hours (including tests/docs)  
**Estimated Agent Time:** 2-3 minutes  

## Description

Create a comprehensive mobile-optimized form component for creating and editing site information. This component must integrate client selection, GPS coordinate capture, address validation, and all site-specific fields while working seamlessly offline with proper validation and user experience.

## Acceptance Criteria

- [ ] Form component created with all required site fields
- [ ] Client selection integration with validation
- [ ] GPS coordinate capture from mobile device
- [ ] UK address validation and postcode lookup
- [ ] Site type classification with appropriate options
- [ ] Mobile-optimized UI using Ionic components
- [ ] Client-side validation with clear error messages
- [ ] Support for offline form submission and queuing
- [ ] Form state management with proper error handling
- [ ] Integration with TypeScript interfaces from 004-001

## Deliverables Checklist

### Form Structure
- [ ] Create SiteForm React component
- [ ] Add client selection dropdown (required)
- [ ] Add site name field (required)
- [ ] Add UK address fields (line 1, line 2, city, county, postcode)
- [ ] Add GPS coordinate fields with capture button
- [ ] Add site type/classification selector
- [ ] Add site description and notes fields
- [ ] Add form actions (save, cancel, reset)

### Client Integration
- [ ] Integrate ClientSelector component
- [ ] Add client validation and error handling
- [ ] Display selected client information
- [ ] Handle client change scenarios
- [ ] Add quick client creation option
- [ ] Implement client context preservation

### GPS and Location Features
- [ ] Implement GPS coordinate capture using device location
- [ ] Add "Use Current Location" button
- [ ] Display coordinates with accuracy information
- [ ] Add manual coordinate entry option
- [ ] Implement coordinate validation
- [ ] Add location accuracy indicators

### Address Validation
- [ ] Implement UK postcode validation
- [ ] Add postcode lookup functionality (online)
- [ ] Auto-populate address fields from postcode
- [ ] Handle manual address entry
- [ ] Validate required address components
- [ ] Add address formatting assistance

### Site Classification
- [ ] Add site type dropdown with UK property types
- [ ] Include options: office, warehouse, residential, industrial, retail, healthcare, education
- [ ] Add custom site type option
- [ ] Implement site type validation
- [ ] Add site type description field
- [ ] Create site type icons and visual indicators

### Validation & Error Handling
- [ ] Implement field-level validation using react-hook-form
- [ ] Add required field validation
- [ ] Add postcode format validation
- [ ] Add GPS coordinate validation
- [ ] Add client selection validation
- [ ] Display clear, helpful error messages
- [ ] Highlight invalid fields with visual indicators

### Mobile Optimization
- [ ] Use Ionic input components for native feel
- [ ] Implement proper keyboard types for different fields
- [ ] Add touch-friendly form controls
- [ ] Ensure form is responsive across device sizes
- [ ] Add loading states and progress indicators
- [ ] Implement proper focus management
- [ ] Add GPS capture with device permissions

### State Management & Integration
- [ ] Integrate with global state management
- [ ] Handle form submission with proper loading states
- [ ] Support for offline form data persistence
- [ ] Queue form submissions when offline
- [ ] Integrate with site data model interfaces
- [ ] Handle form reset and data population for editing

### Testing
- [ ] Write unit tests for form validation
- [ ] Test form submission scenarios
- [ ] Test GPS coordinate capture
- [ ] Test client selection integration
- [ ] Test offline form persistence
- [ ] Test accessibility features
- [ ] Test across different device sizes

## Dependencies

- **Required Before Starting:**
  - 004-001 (Site Data Model and Schema)
  - 003-014 (Client Selection Components)

- **Blocks These Tasks:**
  - 004-003 (GPS Location and Address Management)
  - 004-005 (Site Type Classification System)
  - 004-012 (Site Edit Functionality)

## Technical Considerations

- **Mobile-First Design**: Form must work perfectly on mobile devices with touch interface
- **GPS Integration**: Reliable GPS coordinate capture with proper error handling
- **Offline Support**: Form data must be persistable locally when offline
- **UK-Specific Features**: Postcode validation and address lookup for UK properties
- **Client Relationships**: Proper validation of client-site relationships
- **Performance**: Form should render quickly and handle input efficiently

## User Experience Considerations

- **Professional Appearance**: Form must look professional for business use
- **Location Capture**: Easy GPS coordinate capture with clear accuracy feedback
- **Address Efficiency**: Streamlined address entry with postcode lookup
- **Client Context**: Clear client selection with easy switching
- **Error Feedback**: Clear, non-technical error messages
- **Mobile Workflow**: Optimized for field workers entering site data on location

## Integration Points

- **Client Management**: Client selection and validation
- **GPS Services**: Device location capture and coordinate validation
- **Address Services**: UK postcode lookup and validation
- **Offline Storage**: Form data persistence and sync queue
- **Map Services**: Coordinate validation and location verification

## Definition of Done

- [ ] Form component renders correctly on all target devices
- [ ] All validation rules working correctly
- [ ] GPS coordinate capture functional and tested
- [ ] Client selection integration working smoothly
- [ ] UK address validation implemented and tested
- [ ] Form integrates with offline storage system
- [ ] Unit tests passing with 95%+ coverage
- [ ] Accessibility testing passed
- [ ] Ready for integration with API endpoints and additional features