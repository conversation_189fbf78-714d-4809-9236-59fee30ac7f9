{"name": "@strata/auth", "version": "0.0.1", "private": true, "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "scripts": {"build": "tsup src/index.ts --format cjs,esm --dts", "dev": "tsup src/index.ts --watch --format cjs,esm --dts", "lint": "eslint src/ test/", "test": "vitest run"}, "dependencies": {"@supabase/supabase-js": "^2.50.0"}, "peerDependencies": {"react": "^18.2.0"}, "devDependencies": {"@types/node": "^20.11.19", "@types/react": "^18.2.55", "@types/react-dom": "^18.2.19", "@typescript-eslint/eslint-plugin": "^8.34.1", "@typescript-eslint/parser": "^8.34.1", "eslint": "^8.57.1", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "react": "^18.2.0", "tsup": "^8.0.2", "typescript": "^5.3.3", "vitest": "^1.2.2"}}