module.exports = {
  root: true,
  env: {
    browser: true,
    es2022: true,
    node: true,
  },
  extends: ["eslint:recommended"],
  parserOptions: {
    ecmaVersion: 2022,
    sourceType: "module",
    ecmaFeatures: {
      jsx: true,
    },
  },
  rules: {
    // General rules
    "no-console": "warn",
    "prefer-const": "error",
    "no-unused-vars": "error",
  },
  ignorePatterns: [
    "node_modules/",
    "dist/",
    "build/",
    ".turbo/",
    "*.config.js",
    "*.config.ts",
    "coverage/",
  ],
  // Simplified config - TypeScript checking handled by tsc
  overrides: [
    {
      files: ["**/*.jsx", "**/*.tsx"],
      env: {
        browser: true,
      },
      rules: {
        "no-unused-vars": "off", // TypeScript handles this better
      },
    },
  ],
};
