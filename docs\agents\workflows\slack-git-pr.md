# Agentic Workflow: Slack & Git PRs

This document describes the workflow for agent-driven development using Slack and GitHub pull requests (PRs) in the Strata Compliance project.

## Overview

- Agents receive user stories and requirements (from Slack, GitHub Issues, or docs/product/requirements/user-stories/)
- Agents generate detailed flows, wireframes, and specifications as needed
- Agents create or update documentation and code in feature branches
- Agents open pull requests (PRs) for human review
- Slack notifications are used for PR status, review requests, and approvals

## Step-by-Step Workflow

1. **User Story Intake**

   - User stories are submitted via Slack, GitHub Issues, or added to docs/product/requirements/user-stories/
   - Agents monitor these sources for new work

2. **Specification & Design**

   - Agents create or update:
     - Flows (docs/product/flows/)
     - Wireframes (docs/product/wireframes/)
     - UX docs (docs/product/ux/)
   - Agents may generate prompts for UI/UX tools as needed

3. **Branching & Implementation**

   - Agents create a new Git branch for each feature or doc update
   - All changes are committed with clear, Conventional Commit messages

4. **Pull Request Creation**

   - Agents open a PR targeting the main branch
   - PR description references the relevant user story and acceptance criteria
   - Linked to related flows, wireframes, or UX docs

5. **Review & Approval**

   - Slack notifications alert reviewers of new PRs
   - Reviewers provide feedback or approve
   - Agents address feedback and update PR as needed

6. **Merge & Notification**
   - Once approved, PR is merged
   - Slack notification confirms merge

## Best Practices

- Keep user stories and specs granular and actionable
- Reference all related docs in PRs
- Use clear, descriptive commit messages
- Maintain folder structure for easy navigation

---

This workflow enables scalable, agent-driven development with human-in-the-loop review and Slack-based collaboration.
