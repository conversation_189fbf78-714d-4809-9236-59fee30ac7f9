{"version": 3, "file": "useSupabaseAuth.js", "sourceRoot": "", "sources": ["useSupabaseAuth.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,WAAW,EAAE,SAAS,EAAE,QAAQ,EAAE,MAAM,OAAO,CAAC;AACzD,OAAO,EAAE,QAAQ,EAAE,MAAM,kBAAkB,CAAC;AAE5C,MAAM,UAAU,eAAe;IAC7B,MAAM,CAAC,IAAI,EAAE,OAAO,CAAC,GAAG,QAAQ,CAAc,IAAI,CAAC,CAAC;IACpD,MAAM,CAAC,OAAO,EAAE,UAAU,CAAC,GAAG,QAAQ,CAAiB,IAAI,CAAC,CAAC;IAC7D,MAAM,CAAC,OAAO,EAAE,UAAU,CAAC,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC;IAC7C,MAAM,CAAC,KAAK,EAAE,QAAQ,CAAC,GAAG,QAAQ,CAAmB,IAAI,CAAC,CAAC;IAE3D,SAAS,CAAC,GAAG,EAAE;QACb,OAAO,CAAC,GAAG,CAAC,uCAAuC,CAAC,CAAC;QACrD,QAAQ,CAAC,IAAI;aACV,UAAU,EAAE;aACZ,IAAI,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,EAAE;YACxB,OAAO,CAAC,GAAG,CAAC,gCAAgC,EAAE;gBAC5C,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM;gBACzC,IAAI,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,EAAE,KAAK,IAAI,MAAM;gBACzC,KAAK,EAAE,KAAK,EAAE,OAAO;aACtB,CAAC,CAAC;YACH,UAAU,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,CAAC;YACjC,OAAO,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,IAAI,IAAI,CAAC,CAAC;YACpC,UAAU,CAAC,KAAK,CAAC,CAAC;YAClB,OAAO,CAAC,GAAG,CACT,gCAAgC,EAChC,IAAI,CAAC,OAAO,EAAE,IAAI,EAAE,KAAK,IAAI,MAAM,CACpC,CAAC;QACJ,CAAC,CAAC;aACD,KAAK,CAAC,CAAC,GAAG,EAAE,EAAE;YACb,OAAO,CAAC,KAAK,CAAC,8BAA8B,EAAE,GAAG,CAAC,CAAC;YACnD,UAAU,CAAC,KAAK,CAAC,CAAC;QACpB,CAAC,CAAC,CAAC;QAEL,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,QAAQ,CAAC,IAAI,CAAC,iBAAiB,CACxD,CAAC,MAAM,EAAE,OAAO,EAAE,EAAE;YAClB,OAAO,CAAC,GAAG,CAAC,uBAAuB,EAAE;gBACnC,KAAK,EAAE,MAAM;gBACb,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,KAAK,IAAI,MAAM;aACrC,CAAC,CAAC;YACH,UAAU,CAAC,OAAO,CAAC,CAAC;YACpB,OAAO,CAAC,OAAO,EAAE,IAAI,IAAI,IAAI,CAAC,CAAC;QACjC,CAAC,CACF,CAAC;QAEF,OAAO,GAAG,EAAE;YACV,QAAQ,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC;QACtC,CAAC,CAAC;IACJ,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,MAAM,eAAe,GAAG,WAAW,CACjC,KAAK,EAAE,KAAa,EAAE,QAAgB,EAAE,EAAE;QACxC,UAAU,CAAC,IAAI,CAAC,CAAC;QACjB,QAAQ,CAAC,IAAI,CAAC,CAAC;QACf,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,QAAQ,CAAC,IAAI,CAAC,kBAAkB,CAAC;YACvD,KAAK;YACL,QAAQ;SACT,CAAC,CAAC;QACH,QAAQ,CAAC,KAAK,CAAC,CAAC;QAChB,UAAU,CAAC,KAAK,CAAC,CAAC;QAClB,OAAO,KAAK,CAAC;IACf,CAAC,EACD,EAAE,CACH,CAAC;IAEF,MAAM,mBAAmB,GAAG,WAAW,CAAC,KAAK,EAAE,KAAa,EAAE,EAAE;QAC9D,UAAU,CAAC,IAAI,CAAC,CAAC;QACjB,QAAQ,CAAC,IAAI,CAAC,CAAC;QACf,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC;QAC/D,QAAQ,CAAC,KAAK,CAAC,CAAC;QAChB,UAAU,CAAC,KAAK,CAAC,CAAC;QAClB,OAAO,KAAK,CAAC;IACf,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,MAAM,MAAM,GAAG,WAAW,CAAC,KAAK,EAAE,KAAa,EAAE,QAAgB,EAAE,EAAE;QACnE,UAAU,CAAC,IAAI,CAAC,CAAC;QACjB,QAAQ,CAAC,IAAI,CAAC,CAAC;QACf,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC;YAC3C,KAAK;YACL,QAAQ;SACT,CAAC,CAAC;QACH,QAAQ,CAAC,KAAK,CAAC,CAAC;QAChB,UAAU,CAAC,KAAK,CAAC,CAAC;QAClB,OAAO,KAAK,CAAC;IACf,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,MAAM,OAAO,GAAG,WAAW,CAAC,KAAK,IAAI,EAAE;QACrC,UAAU,CAAC,IAAI,CAAC,CAAC;QACjB,QAAQ,CAAC,IAAI,CAAC,CAAC;QACf,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,QAAQ,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;QAChD,QAAQ,CAAC,KAAK,CAAC,CAAC;QAChB,UAAU,CAAC,KAAK,CAAC,CAAC;QAClB,OAAO,KAAK,CAAC;IACf,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,OAAO;QACL,IAAI;QACJ,OAAO;QACP,OAAO;QACP,KAAK;QACL,eAAe;QACf,mBAAmB;QACnB,MAAM;QACN,OAAO;KACR,CAAC;AACJ,CAAC"}