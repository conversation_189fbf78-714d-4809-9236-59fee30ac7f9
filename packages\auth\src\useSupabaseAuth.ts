import type { AuthError, Session, User } from "@supabase/supabase-js";
import { useCallback, useEffect, useState } from "react";
import { supabase } from "./supabaseClient";

export function useSupabaseAuth() {
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<AuthError | null>(null);

  useEffect(() => {
    console.log("🔧 Supabase auth hook initializing...");
    supabase.auth
      .getSession()
      .then(({ data, error }) => {
        console.log("📡 Supabase getSession result:", {
          session: data.session ? "exists" : "null",
          user: data.session?.user?.email || "null",
          error: error?.message,
        });
        setSession(data.session ?? null);
        setUser(data.session?.user ?? null);
        setLoading(false);
        console.log(
          "✅ Auth loading complete, user:",
          data.session?.user?.email || "null"
        );
      })
      .catch((err) => {
        console.error("❌ Supabase getSession error:", err);
        setLoading(false);
      });

    const { data: listener } = supabase.auth.onAuthStateChange(
      (_event, session) => {
        console.log("🔄 Auth state change:", {
          event: _event,
          user: session?.user?.email || "null",
        });
        setSession(session);
        setUser(session?.user ?? null);
      }
    );

    return () => {
      listener.subscription.unsubscribe();
    };
  }, []);

  const signInWithEmail = useCallback(
    async (email: string, password: string) => {
      setLoading(true);
      setError(null);
      const { error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });
      setError(error);
      setLoading(false);
      return error;
    },
    []
  );

  const signInWithMagicLink = useCallback(async (email: string) => {
    setLoading(true);
    setError(null);
    const { error } = await supabase.auth.signInWithOtp({ email });
    setError(error);
    setLoading(false);
    return error;
  }, []);

  const signUp = useCallback(async (email: string, password: string) => {
    setLoading(true);
    setError(null);
    const { error } = await supabase.auth.signUp({
      email,
      password,
    });
    setError(error);
    setLoading(false);
    return error;
  }, []);

  const signOut = useCallback(async () => {
    setLoading(true);
    setError(null);
    const { error } = await supabase.auth.signOut();
    setError(error);
    setLoading(false);
    return error;
  }, []);

  // Get tenant info from JWT claims
  const getTenantInfo = useCallback(() => {
    if (!session?.access_token) return null;

    try {
      // Decode JWT to get custom claims
      const payload = JSON.parse(atob(session.access_token.split(".")[1]));
      return {
        tenantId: payload.tenant_id || null,
        role: payload.role || null,
        userProfile: payload.user_profile || {},
      };
    } catch (e) {
      console.error("Error decoding JWT:", e);
      return null;
    }
  }, [session]);

  // Check if user has specific role
  const hasRole = useCallback(
    (role: string) => {
      const tenantInfo = getTenantInfo();
      return tenantInfo?.role === role;
    },
    [getTenantInfo]
  );

  // Check if user is tenant admin
  const isTenantAdmin = useCallback(() => {
    return hasRole("TenantAdmin");
  }, [hasRole]);

  // Get tenant info from database (fallback when JWT hooks not available)
  const getTenantInfoFromDB = useCallback(async () => {
    if (!user?.id) return null;

    try {
      const { data, error } = await supabase
        .from("tenant_users")
        .select("tenant_id, role, first_name, last_name, position")
        .eq("user_id", user.id)
        .eq("status", "active")
        .single();

      if (error || !data) return null;

      return {
        tenantId: data.tenant_id,
        role: data.role,
        userProfile: {
          first_name: data.first_name,
          last_name: data.last_name,
          position: data.position,
        },
      };
    } catch (e) {
      console.error("Error fetching tenant info from DB:", e);
      return null;
    }
  }, [user?.id]);

  // Combined tenant info getter (tries JWT first, then DB)
  const getCompleteTenantInfo = useCallback(async () => {
    // First try JWT claims
    const jwtInfo = getTenantInfo();
    if (jwtInfo?.tenantId) {
      return jwtInfo;
    }

    // Fallback to database query
    return await getTenantInfoFromDB();
  }, [getTenantInfo, getTenantInfoFromDB]);

  return {
    user,
    session,
    loading,
    error,
    signInWithEmail,
    signInWithMagicLink,
    signUp,
    signOut,
    getTenantInfo,
    getTenantInfoFromDB,
    getCompleteTenantInfo,
    hasRole,
    isTenantAdmin,
  };
}
