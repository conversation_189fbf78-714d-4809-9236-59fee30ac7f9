// Simple sites endpoint for testing
export async function onRequestGet(context) {
  // Return mock data for testing
  const mockSites = [
    {
      id: "1",
      name: "Test Site 1", 
      description: "A test site for development",
      client: { id: "1", name: "Test Client" },
      address_line_1: "123 Test Street",
      city: "Test City"
    },
    {
      id: "2",
      name: "Test Site 2",
      description: "Another test site", 
      client: { id: "1", name: "Test Client" },
      address_line_1: "456 Demo Avenue",
      city: "Demo City"
    }
  ];

  return new Response(JSON.stringify({ sites: mockSites }), {
    headers: {
      "Content-Type": "application/json",
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
      "Access-Control-Allow-Headers": "Content-Type, Authorization",
    },
  });
}
