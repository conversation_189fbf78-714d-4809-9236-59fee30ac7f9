# Development Environment Setup

Complete guide for setting up the Strata Compliance development environment.

> **📝 Note**: This guide covers **local development** setup only. Production environment configuration is handled during the deployment phase and is outside the scope of initial development setup.

## 🎯 Quick Start

For experienced developers who want to get started immediately:

```bash
# 1. Prerequisites: Node.js 18+, pnpm 8.15.4+
# 2. <PERSON><PERSON> and install
git clone <repository-url>
cd strata-compliance
pnpm install

# 3. Set up environment variables (see Environment Setup section)
# 4. Set up database (see Database Setup section)
# 5. Start development
pnpm dev
```

## 📋 Prerequisites

### Required Software

| Tool        | Version | Purpose            | Installation                        |
| ----------- | ------- | ------------------ | ----------------------------------- |
| **Node.js** | ≥18.0.0 | JavaScript runtime | [nodejs.org](https://nodejs.org/)   |
| **pnpm**    | ≥8.15.4 | Package manager    | `npm install -g pnpm`               |
| **Git**     | Latest  | Version control    | [git-scm.com](https://git-scm.com/) |

### Required Accounts

| Service        | Purpose         | Setup Link                               |
| -------------- | --------------- | ---------------------------------------- |
| **Supabase**   | Database & Auth | [supabase.com](https://supabase.com)     |
| **Cloudflare** | API hosting     | [cloudflare.com](https://cloudflare.com) |

### Optional Tools

| Tool             | Purpose            | Installation                                              |
| ---------------- | ------------------ | --------------------------------------------------------- |
| **Supabase CLI** | Local database     | [docs.supabase.com](https://supabase.com/docs/guides/cli) |
| **VS Code**      | Recommended editor | [code.visualstudio.com](https://code.visualstudio.com/)   |

## 🚀 Installation Steps

### 1. Clone Repository

```bash
git clone <repository-url>
cd strata-compliance
```

### 2. Install Dependencies

```bash
# Install all dependencies for monorepo
pnpm install

# Verify installation
pnpm --version  # Should be 8.15.4+
node --version  # Should be 18.0.0+
```

### 3. Verify Build System

```bash
# Test that everything builds correctly
pnpm build

# Expected output: All packages should build successfully
# ✓ @strata/core:build
# ✓ @strata/ui:build
# ✓ @strata/auth:build
# ✓ @strata/mobile:build
# ✓ @strata/desktop:build
```

## 🔧 Environment Setup

### Environment Variables

Create environment files for each application:

#### Mobile App (`apps/mobile/.env.local`)

```env
# Supabase Configuration
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key

# Development Settings
VITE_APP_ENV=development
VITE_API_BASE_URL=http://localhost:8787
```

#### Desktop App (`apps/desktop/.env.local`)

```env
# Supabase Configuration
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key

# Development Settings
VITE_APP_ENV=development
VITE_API_BASE_URL=http://localhost:8787
```

#### API BFF (`apps/api-bff/.env.local`)

```env
# Supabase Configuration
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# Cloudflare Settings
ENVIRONMENT=development
```

### Getting Supabase Credentials

1. **Create Supabase Project**: Go to [supabase.com](https://supabase.com) and create a new project
2. **Get Project URL**: Found in Project Settings > API
3. **Get Anon Key**: Found in Project Settings > API (public key)
4. **Get Service Role Key**: Found in Project Settings > API (secret key - for server use only)

## 🗄️ Database Setup

### Quick Database Setup

```bash
# Run the database setup script
cd scripts
node setup-staging.js

# Or follow manual setup in database/SETUP.md
```

### Manual Database Setup

See detailed instructions in [`database/SETUP.md`](../database/SETUP.md) for:

- Running SQL migrations
- Setting up authentication
- Configuring Row Level Security
- Creating initial tenant data

## 🏃‍♂️ Development Workflow

### Starting Development Servers

```bash
# Start all applications
pnpm dev

# Start specific applications
pnpm --filter @strata/mobile dev      # Mobile app on http://localhost:5173
pnpm --filter @strata/desktop dev     # Desktop app on http://localhost:5174
pnpm --filter @strata/api-bff dev     # API on http://localhost:8787
```

### Available Scripts

| Command       | Description           | Usage        |
| ------------- | --------------------- | ------------ |
| `pnpm dev`    | Start all dev servers | Development  |
| `pnpm build`  | Build all packages    | CI/Testing   |
| `pnpm test`   | Run all tests         | Testing      |
| `pnpm lint`   | Run linting           | Code quality |
| `pnpm format` | Format code           | Code style   |

### Package-Specific Commands

```bash
# Work with specific packages
pnpm --filter @strata/core build
pnpm --filter @strata/mobile test --watch
pnpm --filter @strata/desktop lint
```

## 🧪 Testing Setup

### Running Tests

```bash
# Run all tests once
pnpm test

# Run tests in watch mode for development
pnpm --filter @strata/core vitest --watch
pnpm --filter @strata/mobile vitest --watch
```

### Test Structure

```
packages/core/test/     # Core business logic tests
apps/mobile/test/       # Mobile app tests
apps/desktop/test/      # Desktop app tests
```

## 🔍 Code Quality

### Linting and Formatting

```bash
# Check code quality
pnpm lint

# Fix auto-fixable issues
pnpm lint --fix

# Format code
pnpm format
```

### TypeScript Configuration

- **Strict mode enabled**: No `any` types allowed
- **Shared config**: `tsconfig.base.json` for consistency
- **Path mapping**: Packages can import each other using `@strata/*`

## 📱 Mobile Development

### Ionic Setup

```bash
# Install Ionic CLI (optional)
npm install -g @ionic/cli

# Run mobile app
cd apps/mobile
ionic serve  # Alternative to pnpm dev
```

### Mobile Testing

- **Browser testing**: Use Chrome DevTools mobile simulation
- **Device testing**: Use Ionic DevApp or build native apps
- **Hot reload**: Changes automatically refresh in browser

## 🖥️ Desktop Development

### Material-UI Setup

The desktop app uses Material-UI (MUI) for components:

- **Theme**: Configured in `apps/desktop/src/theme.ts`
- **Components**: Import from `@mui/material`
- **Icons**: Use `@mui/icons-material`

## 🔧 Troubleshooting

### Common Issues

#### Build Failures

```bash
# Clear node_modules and reinstall
rm -rf node_modules packages/*/node_modules apps/*/node_modules
pnpm install
```

#### TypeScript Errors

```bash
# Check TypeScript configuration
pnpm --filter @strata/core tsc --noEmit
```

#### Database Connection Issues

- Verify Supabase credentials in environment files
- Check database setup in `database/SETUP.md`
- Test connection with `scripts/test-db.js`

#### Port Conflicts

- Mobile app: Default port 5173
- Desktop app: Default port 5174
- API: Default port 8787

Change ports in `vite.config.ts` if needed.

### Quick Fixes

```bash
# Clear everything and reinstall
rm -rf node_modules packages/*/node_modules apps/*/node_modules
pnpm install

# Reset Turbo cache
pnpm turbo clean
pnpm build

# Test database connection
cd scripts && node test-db.js

# Check environment variables
cat apps/mobile/.env.local
cat apps/desktop/.env.local
cat apps/api-bff/.env.local
```

### Getting Help

1. **Check documentation**: Start with `docs/README.md`
2. **Review setup**: Follow `database/SETUP.md` for database issues
3. **Check project organization**: See `docs/project-organization.md`
4. **Verify environment**: Ensure all prerequisites are installed
5. **Use onboarding checklist**: Follow `docs/developer-onboarding-checklist.md`

## 🚀 Next Steps

After setup is complete:

1. **Explore the codebase**: Start with `packages/core` for business logic
2. **Review documentation**: Check `docs/` for project context
3. **Run tests**: Ensure everything works with `pnpm test`
4. **Start developing**: Pick a task from the implementation checklist

## 📚 Additional Resources

- **Project Plan**: [`docs/project-plan-and-cursor-rules.md`](./project-plan-and-cursor-rules.md)
- **Architecture**: [`docs/technical-architecture-plan.md`](./technical-architecture-plan.md)
- **Database Setup**: [`database/SETUP.md`](../database/SETUP.md)
- **Implementation Progress**: [`docs/implementation-checklist.md`](./implementation-checklist.md)
