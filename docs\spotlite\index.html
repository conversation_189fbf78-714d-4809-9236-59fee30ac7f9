<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Spotlite Compliance Screenshots - Interface Analysis</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 1.1em;
        }
        
        .content {
            padding: 30px;
        }
        
        .screenshot-section {
            margin-bottom: 50px;
            border-bottom: 1px solid #eee;
            padding-bottom: 30px;
        }
        
        .screenshot-section:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }
        
        .screenshot-title {
            font-size: 1.8em;
            color: #333;
            margin-bottom: 15px;
            font-weight: 600;
        }
        
        .screenshot-description {
            color: #666;
            margin-bottom: 20px;
            font-size: 1.1em;
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #667eea;
        }
        
        .screenshot-container {
            text-align: center;
            background: #fff;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .screenshot-image {
            max-width: 100%;
            height: auto;
            border-radius: 4px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            transition: transform 0.3s ease;
        }
        
        .screenshot-image:hover {
            transform: scale(1.02);
        }
        
        .filename {
            font-family: 'Courier New', monospace;
            color: #888;
            font-size: 0.9em;
            margin-top: 10px;
        }
        
        .analysis-notes {
            background: #e8f4fd;
            border: 1px solid #b8daff;
            border-radius: 6px;
            padding: 20px;
            margin: 30px 0;
        }
        
        .analysis-notes h3 {
            color: #0056b3;
            margin-top: 0;
        }
        
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 30px;
            margin-top: 30px;
        }
        
        @media (max-width: 768px) {
            .grid {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .content {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Spotlite Compliance Interface Analysis</h1>
            <p>Screenshots from existing single-tenant product to inspire Strata Compliance webtop design</p>
        </div>
        
        <div class="content">
            <div class="analysis-notes">
                <h3>🎯 Analysis Purpose</h3>
                <p>These screenshots from Spotlite Compliance (single-tenant) will inform the design of Strata Compliance's webtop version, particularly:</p>
                <ul>
                    <li><strong>Client Portal Design</strong> - How clients view their inspection data</li>
                    <li><strong>Document Management</strong> - Organization and presentation of reports</li>
                    <li><strong>Site/Building Hierarchy</strong> - Multi-building site management</li>
                    <li><strong>User Journey Flows</strong> - Navigation patterns and workflows</li>
                    <li><strong>License Tier Features</strong> - Professional vs Enterprise capabilities</li>
                </ul>
            </div>
            
            <div class="screenshot-section">
                <div class="screenshot-title">📋 Client List Overview</div>
                <div class="screenshot-description">
                    Main client management interface showing how multiple clients are organized and accessed. This informs the Enterprise tier multi-client management capabilities.
                </div>
                <div class="screenshot-container">
                    <img src="Client_List.png" alt="Client List Interface" class="screenshot-image">
                    <div class="filename">Client_List.png</div>
                </div>
            </div>
            
            <div class="screenshot-section">
                <div class="screenshot-title">👤 Individual Client View</div>
                <div class="screenshot-description">
                    Detailed view of a single client showing their information, properties, and available actions. Key for understanding client portal entry points.
                </div>
                <div class="screenshot-container">
                    <img src="Client_View.png" alt="Client View Interface" class="screenshot-image">
                    <div class="filename">Client_View.png</div>
                </div>
            </div>
            
            <div class="screenshot-section">
                <div class="screenshot-title">🏢 Client Properties Management</div>
                <div class="screenshot-description">
                    How properties are organized under each client. Critical for understanding the Client → Site → Building hierarchy that will inform our data structure.
                </div>
                <div class="screenshot-container">
                    <img src="Client_Properties.png" alt="Client Properties Interface" class="screenshot-image">
                    <div class="filename">Client_Properties.png</div>
                </div>
            </div>
            
            <div class="screenshot-section">
                <div class="screenshot-title">🏗️ Client Sites Overview</div>
                <div class="screenshot-description">
                    Site-level management showing how multiple buildings and locations are organized. Essential for Enterprise tier multi-site project management.
                </div>
                <div class="screenshot-container">
                    <img src="Client_Sites.png" alt="Client Sites Interface" class="screenshot-image">
                    <div class="filename">Client_Sites.png</div>
                </div>
            </div>
            
            <div class="screenshot-section">
                <div class="screenshot-title">📍 Individual Site View</div>
                <div class="screenshot-description">
                    Detailed site interface showing buildings, documents, and site-specific information. Key for understanding work package construction and deployment.
                </div>
                <div class="screenshot-container">
                    <img src="Client_Site_View.png" alt="Client Site View Interface" class="screenshot-image">
                    <div class="filename">Client_Site_View.png</div>
                </div>
            </div>
            
            <div class="screenshot-section">
                <div class="screenshot-title">🏠 Property Detail View</div>
                <div class="screenshot-description">
                    Individual property/building interface showing documents, floor plans, and inspection history. Critical for client portal design and report presentation.
                </div>
                <div class="screenshot-container">
                    <img src="Client_Property_View.png" alt="Client Property View Interface" class="screenshot-image">
                    <div class="filename">Client_Property_View.png</div>
                </div>
            </div>
            
            <div class="grid">
                <div class="screenshot-section">
                    <div class="screenshot-title">📄 Document Management</div>
                    <div class="screenshot-description">
                        Document list interface showing how reports and certificates are organized and accessed by clients.
                    </div>
                    <div class="screenshot-container">
                        <img src="Client_Property_DocumentList.png" alt="Document List Interface" class="screenshot-image">
                        <div class="filename">Client_Property_DocumentList.png</div>
                    </div>
                </div>
                
                <div class="screenshot-section">
                    <div class="screenshot-title">📤 Document Upload</div>
                    <div class="screenshot-description">
                        Document upload interface showing categorization and metadata management for compliance documents.
                    </div>
                    <div class="screenshot-container">
                        <img src="Client_Property_UploadDocument.png" alt="Document Upload Interface" class="screenshot-image">
                        <div class="filename">Client_Property_UploadDocument.png</div>
                    </div>
                </div>
                
                <div class="screenshot-section">
                    <div class="screenshot-title">🗺️ Floor Plan Management</div>
                    <div class="screenshot-description">
                        Floor plan upload and management interface. Essential for Enterprise tier site plan management capabilities.
                    </div>
                    <div class="screenshot-container">
                        <img src="Client_Property_UploadFloorPlan.png" alt="Floor Plan Upload Interface" class="screenshot-image">
                        <div class="filename">Client_Property_UploadFloorPlan.png</div>
                    </div>
                </div>
                
                <div class="screenshot-section">
                    <div class="screenshot-title">⚠️ Asbestos Management</div>
                    <div class="screenshot-description">
                        Specialized asbestos document management showing compliance-specific categorization and workflows.
                    </div>
                    <div class="screenshot-container">
                        <img src="Client_Property_AddDocument_AsbestosManagement.png" alt="Asbestos Management Interface" class="screenshot-image">
                        <div class="filename">Client_Property_AddDocument_AsbestosManagement.png</div>
                    </div>
                </div>
            </div>
            
            <div class="analysis-notes">
                <h3>🔍 Key Analysis Points</h3>
                <p>When reviewing these screenshots, focus on:</p>
                <ul>
                    <li><strong>Navigation Patterns</strong> - How users move between clients, sites, and documents</li>
                    <li><strong>Data Hierarchy</strong> - Client → Site → Building → Document organization</li>
                    <li><strong>Document Categorization</strong> - How different report types are organized</li>
                    <li><strong>User Interface Elements</strong> - Buttons, forms, and layout patterns</li>
                    <li><strong>Client Portal Features</strong> - What clients can see and do</li>
                    <li><strong>Professional Presentation</strong> - How data is formatted for client consumption</li>
                </ul>
            </div>
        </div>
    </div>
</body>
</html>
