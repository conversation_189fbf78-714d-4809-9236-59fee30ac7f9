#!/usr/bin/env node

/**
 * Setup script for Strata Compliance staging environment
 * This script helps configure Supabase and run migrations
 */

import { createClient } from '@supabase/supabase-js'
import fs from 'fs'
import path from 'path'

const SUPABASE_URL = 'https://fwktrittbrmqarkipcpz.supabase.co'
const SUPABASE_SERVICE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ3a3RyaXR0YnJtcWFya2lwY3B6Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MDE4OTE5NSwiZXhwIjoyMDY1NzY1MTk1fQ.ldI65o8yJCY76i2LRi8eZA_ljv2peuomIyA9sucAg0M'

// Create Supabase client with service role for admin operations
const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY)

async function createEnvFile() {
  const envContent = `# Supabase Staging Environment
NEXT_PUBLIC_SUPABASE_URL=https://fwktrittbrmqarkipcpz.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ3a3RyaXR0YnJtcWFya2lwY3B6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAxODkxOTUsImV4cCI6MjA2NTc2NTE5NX0.cbfaZXV4NwBwRXVSRF0xSzWVUTmfXlwZa4ptuOF0se0

# Vite (for any Vite apps)
VITE_SUPABASE_URL=https://fwktrittbrmqarkipcpz.supabase.co
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ3a3RyaXR0YnJtcWFya2lwY3B6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAxODkxOTUsImV4cCI6MjA2NTc2NTE5NX0.cbfaZXV4NwBwRXVSRF0xSzWVUTmfXlwZa4ptuOF0se0

# Service role key (for server-side operations, migrations, etc.)
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ3a3RyaXR0YnJtcWFya2lwY3B6Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MDE4OTE5NSwiZXhwIjoyMDY1NzY1MTk1fQ.ldI65o8yJCY76i2LRi8eZA_ljv2peuomIyA9sucAg0M
`

  fs.writeFileSync('.env.local', envContent)
  console.log('✅ Created .env.local with staging credentials')
}

async function runMigration(migrationPath, migrationName) {
  try {
    console.log(`🔄 Running migration: ${migrationName}`)
    
    const migrationSQL = fs.readFileSync(migrationPath, 'utf-8')
    const { error } = await supabase.rpc('exec_sql', { sql: migrationSQL })
    
    if (error) {
      // Try direct query execution for simpler migrations
      const { error: directError } = await supabase.query(migrationSQL)
      if (directError) {
        console.error(`❌ Error in ${migrationName}:`, error || directError)
        return false
      }
    }
    
    console.log(`✅ Successfully ran: ${migrationName}`)
    return true
  } catch (err) {
    console.error(`❌ Error running ${migrationName}:`, err.message)
    return false
  }
}

async function runAllMigrations() {
  const migrationsDir = path.join(process.cwd(), 'database', 'migrations')
  const migrations = [
    '001_create_tenants.sql',
    '002_create_tenant_users.sql', 
    '003_create_clients.sql',
    '004_create_properties.sql',
    '005_create_sites_and_inspections.sql'
  ]

  console.log('🚀 Starting database migrations...')
  
  for (const migration of migrations) {
    const migrationPath = path.join(migrationsDir, migration)
    if (fs.existsSync(migrationPath)) {
      const success = await runMigration(migrationPath, migration)
      if (!success) {
        console.log('❌ Migration failed. Please check the Supabase SQL editor and run manually.')
        return false
      }
    } else {
      console.log(`⚠️  Migration file not found: ${migration}`)
    }
  }
  
  console.log('✅ All migrations completed!')
  return true
}

async function createSampleData() {
  console.log('🔄 Creating sample data...')
  
  try {
    // Create first tenant
    const { data: tenant, error: tenantError } = await supabase
      .from('tenants')
      .insert({
        name: 'Strata Compliance Demo',
        slug: 'demo-company',
        subscription_tier: 'pro'
      })
      .select()
      .single()

    if (tenantError) {
      console.error('❌ Error creating tenant:', tenantError)
      return false
    }

    console.log('✅ Created tenant:', tenant.name)

    // Create sample client
    const { data: client, error: clientError } = await supabase
      .from('clients')
      .insert({
        tenant_id: tenant.id,
        name: 'Riverside Apartments',
        client_type: 2,
        emergency_contact_details: 'Emergency: 0800 123 4567',
        building_name: 'Riverside Court',
        building_number: '123'
      })
      .select()
      .single()

    if (clientError) {
      console.error('❌ Error creating client:', clientError)
      return false
    }

    console.log('✅ Created client:', client.name)

    // Create sample site
    const { data: site, error: siteError } = await supabase
      .from('sites')
      .insert({
        tenant_id: tenant.id,
        client_id: client.id,
        name: 'Riverside Complex',
        description: 'Main residential complex',
        address_line_1: '123 River Street',
        city: 'London',
        postal_code: 'SW1A 1AA',
        country: 'UK'
      })
      .select()
      .single()

    if (siteError) {
      console.error('❌ Error creating site:', siteError)
      return false
    }

    console.log('✅ Created site:', site.name)

    // Create sample properties
    const properties = [
      { unit: 'Flat 1A', floor: 'Ground Floor' },
      { unit: 'Flat 1B', floor: 'Ground Floor' },
      { unit: 'Flat 2A', floor: 'First Floor' },
      { unit: 'Common Areas', floor: 'All Floors' }
    ]

    for (const prop of properties) {
      const { data: property, error: propError } = await supabase
        .from('properties')
        .insert({
          tenant_id: tenant.id,
          client_id: client.id,
          site_id: site.id,
          property_code: `RIV-${prop.unit.replace(/\s+/g, '-')}`,
          unit: prop.unit,
          floor: prop.floor,
          is_managed: true
        })
        .select()
        .single()

      if (propError) {
        console.error(`❌ Error creating property ${prop.unit}:`, propError)
      } else {
        console.log(`✅ Created property: ${property.unit}`)
      }
    }

    console.log('✅ Sample data creation completed!')
    console.log('')
    console.log('📋 Next steps:')
    console.log('1. Go to https://fwktrittbrmqarkipcpz.supabase.co')
    console.log('2. Sign in and check your tables in the Table Editor')
    console.log('3. Create a user account and add them to tenant_users table')
    console.log('4. Start building your app!')
    
    return true
  } catch (err) {
    console.error('❌ Error creating sample data:', err.message)
    return false
  }
}

async function main() {
  console.log('🚀 Strata Compliance - Staging Setup')
  console.log('===================================')
  
  // Create environment file
  createEnvFile()
  
  // Test connection
  console.log('🔄 Testing Supabase connection...')
  const { data, error } = await supabase.from('_temp_test').select().limit(1)
  if (error && !error.message.includes('relation "_temp_test" does not exist')) {
    console.error('❌ Connection failed:', error.message)
    return
  }
  console.log('✅ Supabase connection successful')
  
  // Run migrations
  const migrationsSuccess = await runAllMigrations()
  if (!migrationsSuccess) {
    console.log('')
    console.log('⚠️  Some migrations failed. You can run them manually in the Supabase SQL editor:')
    console.log('   1. Go to https://fwktrittbrmqarkipcpz.supabase.co')
    console.log('   2. Navigate to SQL Editor') 
    console.log('   3. Run each migration file from database/migrations/ in order')
    return
  }
  
  // Create sample data
  await createSampleData()
}

main().catch(console.error) 