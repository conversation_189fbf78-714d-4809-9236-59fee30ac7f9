#!/usr/bin/env node

import { createClient } from "@supabase/supabase-js";

const supabaseUrl = "https://fwktrittbrmqarkipcpz.supabase.co";
const serviceKey =
  "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ3a3RyaXR0YnJtcWFya2lwY3B6Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MDE4OTE5NSwiZXhwIjoyMDY1NzY1MTk1fQ.ldI65o8yJCY76i2LRi8eZA_ljv2peuomIyA9sucAg0M";
const anonKey =
  "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ3a3RyaXR0YnJtcWFya2lwY3B6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAxODkxOTUsImV4cCI6MjA2NTc2NTE5NX0.cbfaZXV4NwBwRXVSRF0xSzWVUTmfXlwZa4ptuOF0se0";

const supabase = createClient(supabaseUrl, serviceKey);
const anonClient = createClient(supabaseUrl, anonKey);

async function validateRLSSetup() {
  console.log("🔒 Validating RLS Setup for Strata Compliance\n");

  // 1. Check if RLS helper function exists and works
  console.log("📋 Step 1: Checking RLS Status Function...");
  try {
    const { data: rlsStatus, error } = await supabase.rpc("check_rls_status");

    if (error) {
      console.log("❌ check_rls_status function not available:", error.message);
      console.log(
        "   This function should have been created by the RLS setup script"
      );
      return false;
    } else {
      console.log("✅ check_rls_status function is working");
      console.log("\n📊 RLS Status by Table:");
      rlsStatus?.forEach((table) => {
        const status = table.rls_enabled ? "✅ ENABLED" : "❌ DISABLED";
        console.log(
          `   ${table.table_name.padEnd(15)}: ${status} (${table.policy_count} policies)`
        );
      });
    }
  } catch (e) {
    console.log("❌ Error calling check_rls_status:", e.message);
    return false;
  }

  // 2. Validate expected policy counts
  console.log("\n📋 Step 2: Validating Policy Counts...");

  const expectedPolicyCounts = {
    tenants: 2,
    tenant_users: 3,
    clients: 2,
    properties: 3,
    sites: 2,
    inspections: 4,
  };

  let policyCountsCorrect = true;

  try {
    const { data: rlsStatus } = await supabase.rpc("check_rls_status");

    for (const table of rlsStatus) {
      const expected = expectedPolicyCounts[table.table_name];
      const actual = table.policy_count;

      if (actual === expected) {
        console.log(`   ✅ ${table.table_name}: ${actual} policies (correct)`);
      } else {
        console.log(
          `   ❌ ${table.table_name}: ${actual} policies (expected ${expected})`
        );
        policyCountsCorrect = false;
      }
    }

    if (!policyCountsCorrect) {
      console.log("\n⚠️  Policy counts don't match expected values");
      console.log("   This might indicate missing or extra policies");
    }
  } catch (e) {
    console.log("❌ Error validating policy counts:", e.message);
    return false;
  }

  // 3. Test RLS enforcement with anonymous access
  console.log("\n📋 Step 3: Testing RLS Enforcement...");
  const tables = [
    "tenants",
    "tenant_users",
    "clients",
    "properties",
    "sites",
    "inspections",
  ];

  let rlsWorking = true;

  for (const table of tables) {
    try {
      const { data, error } = await anonClient.from(table).select("*").limit(1);

      if (error) {
        // This is what we want - RLS should block anonymous access
        if (error.message.includes("row-level security policy")) {
          console.log(`   ✅ ${table}: RLS blocking anonymous access`);
        } else {
          console.log(
            `   ⚠️  ${table}: Blocked but unexpected error: ${error.message}`
          );
        }
      } else {
        // RLS can work in two ways:
        // 1. Throw an error (strict mode)
        // 2. Return empty results (permissive mode)
        // Both are valid - returning 0 rows means RLS is working
        const rowCount = data?.length || 0;
        if (rowCount === 0) {
          console.log(
            `   ✅ ${table}: RLS working (returned ${rowCount} rows)`
          );
        } else {
          console.log(
            `   ❌ ${table}: Anonymous access allowed! Returned ${rowCount} rows`
          );
          rlsWorking = false;
        }
      }
    } catch (e) {
      console.log(`   ✅ ${table}: RLS blocking access (${e.message})`);
    }
  }

  // 4. Test helper functions
  console.log("\n📋 Step 4: Testing Helper Functions...");
  try {
    // Test get_user_tenant_info function
    const testUserId = "00000000-0000-0000-0000-000000000000"; // Dummy UUID
    const { data, error } = await supabase.rpc("get_user_tenant_info", {
      user_id: testUserId,
    });

    if (error) {
      console.log("❌ get_user_tenant_info function error:", error.message);
    } else {
      console.log("✅ get_user_tenant_info function is working");
      console.log(`   Returned ${data?.length || 0} rows for test user`);
    }
  } catch (e) {
    console.log("❌ Error testing helper functions:", e.message);
  }

  // 5. Summary
  console.log("\n📊 Validation Summary:");
  if (rlsWorking && policyCountsCorrect) {
    console.log(
      "✅ RLS is properly configured and enforcing security policies"
    );
    console.log("✅ All expected policies are in place");
    console.log("✅ Anonymous access is properly blocked");
    console.log("\n🎉 Your RLS setup is working correctly!");
    return true;
  } else {
    console.log("❌ RLS has issues that need to be addressed");
    console.log("\n🔧 Issues found:");
    if (!rlsWorking) {
      console.log("- RLS enforcement is not working properly");
    }
    if (!policyCountsCorrect) {
      console.log("- Policy counts don't match expected values");
    }
    console.log("\n🔧 Next steps:");
    console.log("1. Check if the RLS setup script ran completely");
    console.log("2. Verify all policies were created successfully");
    console.log("3. Test with authenticated users to ensure proper access");
    return false;
  }
}

validateRLSSetup().catch(console.error);
