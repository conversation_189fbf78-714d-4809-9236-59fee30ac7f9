import { IonApp, IonRouterOutlet, setupIonicReact } from '@ionic/react';
import { IonReactRouter } from '@ionic/react-router';
import { useSupabaseAuth } from '@strata/auth';
import { createRoot } from 'react-dom/client';
import { Redirect, Route, Switch } from 'react-router-dom';

// Import React and components
import { useEffect, useState } from 'react';
import AppLoading from './components/AppLoading';
import ProtectedRoute from './components/ProtectedRoute';
import { debugApp } from './lib/logging';
import AuthPage from './pages/AuthPage';
import DashboardPage from './pages/DashboardPage';

/* Core CSS required for Ionic components to work properly */
import '@ionic/react/css/core.css';

/* Basic CSS for apps built with Ionic */
import '@ionic/react/css/normalize.css';
import '@ionic/react/css/structure.css';
import '@ionic/react/css/typography.css';

/* Optional CSS utils that can be commented out */
import '@ionic/react/css/display.css';
import '@ionic/react/css/flex-utils.css';
import '@ionic/react/css/float-elements.css';
import '@ionic/react/css/padding.css';
import '@ionic/react/css/text-alignment.css';
import '@ionic/react/css/text-transformation.css';

/**
 * Ionic Dark Mode
 * -----------------------------------------------------
 * For more info, please see:
 * https://ionicframework.com/docs/theming/dark-mode
 */

/* Dark mode palettes - commented out for now */
/* import '@ionic/react/css/palettes/dark.always.css'; */
/* import '@ionic/react/css/palettes/dark.class.css'; */
/* import '@ionic/react/css/palettes/dark.system.css'; */

/* Theme variables */
import './theme/variables.css';

setupIonicReact();

// Main App component with authentication flow
const App = () => {
  const { user, loading, error, signInWithEmail, signInWithMagicLink, signUp, signOut } = useSupabaseAuth();
  const [initialLoadComplete, setInitialLoadComplete] = useState(false);

  // Track when initial auth check is complete
  useEffect(() => {
    if (!loading && !initialLoadComplete) {
      setInitialLoadComplete(true);
    }
  }, [loading, initialLoadComplete]);

  // Debug logging
  debugApp('App state', { user: user?.email || 'null', loading, initialLoadComplete });

  // Only show global loading spinner for initial auth check, not for user actions
  if (!initialLoadComplete) {
    debugApp('Showing app loading spinner for initial auth check');
    return (
      <IonApp>
        <AppLoading />
      </IonApp>
    );
  }

  return (
    <IonApp>
      <IonReactRouter>
        <div id="main">
          <IonRouterOutlet>
            <Switch>
              <Route
                path="/auth"
                render={() => (
                  <AuthPage
                    user={user}
                    loading={loading}
                    error={error}
                    signInWithEmail={signInWithEmail}
                    signInWithMagicLink={signInWithMagicLink}
                    signUp={signUp}
                  />
                )}
              />
              <ProtectedRoute
                path="/dashboard"
                component={() => <DashboardPage user={user} signOut={signOut} />}
                user={user}
                loading={loading}
              />
              <Route exact path="/">
                {user ? <Redirect to="/dashboard" /> : <Redirect to="/auth" />}
              </Route>
            </Switch>
          </IonRouterOutlet>
        </div>
      </IonReactRouter>
    </IonApp>
  );
};

// Render the app
const container = document.getElementById('root');
const root = createRoot(container!);
root.render(<App />);