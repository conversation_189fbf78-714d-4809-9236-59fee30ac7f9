# Strata Compliance – Single User App UX Design (Basic License)

## Introduction

This document defines the precise, screen-by-screen UX design for a single-user version of the Strata Compliance app. It is intended for agents and developers to implement a best-in-class mobile experience, drawing on the strongest features of Flow Mobile Surveying and PocketSurvey. The user is a solo operator, running their entire survey business from the app, with no web dashboard or team features. All essential business functions must be accessible and efficient.

---

## Outline

1. Onboarding & Account Setup
2. Home / Dashboard
3. Job/Survey Creation
4. On-Site Data Capture
5. Photo & Media Management
6. Offline Mode & Sync
7. Report Generation & Delivery
8. Client & Site Management
9. Scheduling & Calendar
10. Settings & Support

---

## 1. Onboarding & Account Setup

- **Welcome Screen:** Branded, simple welcome with app value proposition.
- **Sign Up / Login:** Email/password registration. Option to import company logo and contact details for report branding.
- **Guided Tour:** Brief, mobile-optimized walkthrough of key features (survey creation, reporting, offline, support).
- **First Job Prompt:** After setup, prompt user to create their first job immediately.

## 2. Home / Dashboard

- **Overview:** Clean dashboard showing:
  - Upcoming jobs (today/this week)
  - Recent activity (last jobs, reports sent)
  - Quick actions: “New Survey”, “Add Client”, “View Calendar”
- **Navigation:** Bottom tab bar for: Home, Jobs, Clients, Calendar, Settings
- **Status Indicators:** Color-coded job statuses (scheduled, in progress, completed, overdue)

## 3. Job/Survey Creation

- **New Job Button:** Prominent on dashboard and jobs tab.
- **Job Form:**
  - Select client (or add new)
  - Select site/property (or add new)
  - Choose survey template (pre-built, e.g., asbestos, fire, condition)
  - Set date/time
  - Optional notes
- **Template Selection:** Pre-built, compliance-ready templates with minimal required fields for fast start.

## 4. On-Site Data Capture

- **Survey Workflow:**
  - Logical sections (site details, findings, recommendations, photos)
  - Large touch targets, fast navigation
  - Real-time validation for required fields
  - Progress bar for survey completion
- **Add Items:** Add new inspection items (e.g., rooms, elements, hazards) with location, description, status
- **Custom Fields:** Allow user to add custom notes or fields as needed

## 5. Photo & Media Management

- **Integrated Camera:** Capture photos directly in survey flow
- **Photo Annotation:** Add notes or markups to images
- **Gallery View:** Review, delete, or reorder photos before report generation
- **Attach to Items:** Link photos to specific survey items or findings

## 6. Offline Mode & Sync

- **Offline Banner:** Clear indication when offline; all features remain available
- **Auto Sync:** Data syncs automatically when connection is restored
- **Conflict Handling:** Simple, user-friendly resolution if sync conflicts occur

## 7. Report Generation & Delivery

- **One-Click PDF:** Prominent button to generate branded PDF report from completed survey
- **Preview:** Preview report before sending
- **Custom Branding:** Company logo, contact details, and cover page
- **Send/Share:** Email report to client or share link directly from app
- **Report History:** View sent reports and delivery status

## 8. Client & Site Management

- **Client List:** Searchable, filterable list of clients
- **Add/Edit Client:** Simple form for contact details, company info
- **Site/Property List:** Linked to clients; add/edit properties with address, notes, and photos
- **Client Detail View:** See all jobs, reports, and sites for a client

## 9. Scheduling & Calendar

- **Calendar View:** Month/week/day views of scheduled jobs
- **Add/Edit Job:** Tap on date to schedule new job
- **Reminders:** In-app and push notifications for upcoming jobs
- **Color Coding:** Jobs color-coded by status

## 10. Settings & Support

- **Profile:** Edit user info, company branding, notification preferences
- **App Settings:** Control report fields, template options, and feature toggles
- **Help & Support:** In-app help topics, FAQs, and direct contact to support team
- **Legal:** Access privacy policy, terms, and compliance info

---

## Notes

- All features are optimized for mobile, single-user operation
- No team, multi-user, or web dashboard features are included
- All business-critical workflows (survey, reporting, client management, scheduling) are accessible from the app
- Progressive onboarding and contextual help are available throughout

---

## Next Steps

- This document can be used to create user stories, wireframes, and implementation tasks for agents and developers
- Further detail can be added as more information or user feedback becomes available
