#!/usr/bin/env node

/**
 * Test Authentication and RLS Configuration
 * This script tests JWT claims and role-based access control
 */

import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://fwktrittbrmqarkipcpz.supabase.co';
const anonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ3a3RyaXR0YnJtcWFya2lwY3B6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAxODkxOTUsImV4cCI6MjA2NTc2NTE5NX0.cbfaZXV4NwBwRXVSRF0xSzWVUTmfXlwZa4ptuOF0se0';
const serviceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ3a3RyaXR0YnJtcWFya2lwY3B6Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MDE4OTE5NSwiZXhwIjoyMDY1NzY1MTk1fQ.ldI65o8yJCY76i2LRi8eZA_ljv2peuomIyA9sucAg0M';

const anonClient = createClient(supabaseUrl, anonKey);
const serviceClient = createClient(supabaseUrl, serviceKey);

async function testRLSEnforcement() {
  console.log('🔒 Testing RLS Enforcement...');
  
  const tables = ['tenants', 'tenant_users', 'clients', 'properties', 'sites', 'inspections'];
  
  for (const table of tables) {
    try {
      const { data, error } = await anonClient.from(table).select('*').limit(1);
      
      if (error) {
        if (error.message.includes('RLS') || error.message.includes('policy')) {
          console.log(`   ✅ ${table}: RLS working (${error.message})`);
        } else {
          console.log(`   ⚠️  ${table}: Unexpected error (${error.message})`);
        }
      } else {
        console.log(`   ❌ ${table}: RLS not enforced (returned ${data?.length || 0} rows)`);
      }
    } catch (e) {
      console.log(`   ✅ ${table}: RLS working (access denied)`);
    }
  }
}

async function checkJWTHook() {
  console.log('\n🔑 Checking JWT Hook Configuration...');
  
  try {
    // Check if the JWT hook function exists
    const { data, error } = await serviceClient.rpc('check_function_exists', {
      function_name: 'custom_access_token_hook',
      schema_name: 'auth'
    });
    
    if (error) {
      console.log('   ⚠️  Cannot verify JWT hook via RPC');
      console.log('   💡 Please verify manually in Supabase Dashboard > Authentication > Hooks');
    } else {
      console.log('   ✅ JWT hook function verification attempted');
    }
    
  } catch (e) {
    console.log('   ⚠️  Cannot verify JWT hook programmatically');
    console.log('   💡 Please check Supabase Dashboard > Authentication > Hooks');
  }
}

async function checkSampleData() {
  console.log('\n📊 Checking Sample Data...');
  
  try {
    // Check tenants
    const { data: tenants, error: tenantsError } = await serviceClient
      .from('tenants')
      .select('*')
      .limit(5);
      
    if (tenantsError) {
      console.log(`   ❌ Error fetching tenants: ${tenantsError.message}`);
    } else {
      console.log(`   📋 Found ${tenants?.length || 0} tenants`);
      if (tenants && tenants.length > 0) {
        tenants.forEach(tenant => {
          console.log(`      - ${tenant.name} (${tenant.slug})`);
        });
      }
    }
    
    // Check tenant_users
    const { data: users, error: usersError } = await serviceClient
      .from('tenant_users')
      .select('*')
      .limit(5);
      
    if (usersError) {
      console.log(`   ❌ Error fetching tenant users: ${usersError.message}`);
    } else {
      console.log(`   👥 Found ${users?.length || 0} tenant users`);
    }
    
  } catch (e) {
    console.log(`   ❌ Error checking sample data: ${e.message}`);
  }
}

async function testUserSignup() {
  console.log('\n👤 Testing User Signup Flow...');
  
  console.log('   📋 To test complete authentication:');
  console.log('   1. Sign up a user through your app');
  console.log('   2. Add user to tenant_users table:');
  console.log('      ```sql');
  console.log('      INSERT INTO tenant_users (tenant_id, user_id, role, first_name, last_name, status)');
  console.log('      VALUES (');
  console.log('        \'da8d1551-8af3-4d44-a904-68e7bb2997a3\',  -- Demo tenant ID');
  console.log('        \'USER_ID_FROM_AUTH_USERS\',               -- From auth.users table');
  console.log('        \'TenantAdmin\',                           -- Role');
  console.log('        \'Test\',                                  -- First name');
  console.log('        \'User\',                                  -- Last name');
  console.log('        \'active\'                                 -- Status');
  console.log('      );');
  console.log('      ```');
  console.log('   3. Sign in and check JWT contains tenant_id and role');
  console.log('   4. Test that RLS policies allow access to tenant data');
}

async function generateTestQueries() {
  console.log('\n🧪 Test Queries for Manual Verification...');
  
  console.log('\n-- Check RLS is enabled:');
  console.log('SELECT schemaname, tablename, rowsecurity FROM pg_tables WHERE schemaname = \'public\';');
  
  console.log('\n-- Check policies exist:');
  console.log('SELECT tablename, policyname, cmd FROM pg_policies WHERE schemaname = \'public\';');
  
  console.log('\n-- Check JWT claims (run when signed in):');
  console.log('SELECT auth.jwt();');
  
  console.log('\n-- Check user tenant relationship:');
  console.log('SELECT * FROM tenant_users WHERE user_id = auth.uid();');
  
  console.log('\n-- Test tenant data access (should only show user\'s tenant):');
  console.log('SELECT * FROM tenants;');
  console.log('SELECT * FROM clients;');
}

async function main() {
  console.log('🧪 Strata Compliance Authentication Test');
  console.log('=========================================\n');
  
  // Test RLS enforcement
  await testRLSEnforcement();
  
  // Check JWT hook
  await checkJWTHook();
  
  // Check sample data
  await checkSampleData();
  
  // Test user signup instructions
  await testUserSignup();
  
  // Generate test queries
  await generateTestQueries();
  
  console.log('\n📋 Next Steps:');
  console.log('1. Run database/auth-setup.sql in Supabase SQL Editor');
  console.log('2. Enable JWT hook in Authentication > Hooks');
  console.log('3. Test user signup and signin flow');
  console.log('4. Verify JWT contains tenant_id and role claims');
  console.log('5. Test that RLS policies work correctly');
}

main().catch(console.error);
