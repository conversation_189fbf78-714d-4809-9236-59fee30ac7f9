import { Page, Locator, expect } from '@playwright/test';

export class DashboardPage {
  readonly page: Page;
  readonly signOutButton: Locator;
  readonly userInfo: Locator;

  constructor(page: Page) {
    this.page = page;
    
    // Based on your dashboard structure
    this.signOutButton = page.locator('ion-button:has-text("Sign Out")');
    this.userInfo = page.locator('text=<EMAIL>');
  }

  async goto() {
    await this.page.goto('/dashboard');
  }

  async signOut() {
    await this.signOutButton.click();
  }

  async expectUserLoggedIn(email?: string) {
    if (email) {
      await expect(this.page.locator(`text=${email}`)).toBeVisible();
    } else {
      // Just check we're on dashboard
      await expect(this.page).toHaveURL(/\/dashboard/);
    }
  }

  async waitForLoad() {
    await this.page.waitForLoadState('networkidle');
  }
}
