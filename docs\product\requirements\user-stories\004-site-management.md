# User Story: Site Management

- **ID:** 004-site-management  
- **Title:** Create and manage site information with location data
- **As a:** solo surveyor
- **I want to:** add and organize site information for my clients
- **So that:** I can efficiently plan and conduct inspections at specific locations

## Acceptance Criteria

- [ ] User can create new sites linked to existing clients
- [ ] User can enter site address with GPS coordinates
- [ ] User can upload site plans/floor plans (PDF/image formats)
- [ ] User can add site-specific notes and access instructions
- [ ] User can specify site type (office, warehouse, residential, etc.)
- [ ] User can add key holder contact information
- [ ] User can set site-specific health & safety requirements
- [ ] User can view sites on a map (when online)
- [ ] User can search and filter sites by client, location, or type
- [ ] Site data is stored locally and synced when online
- [ ] User can view site history (past inspections, reports)

## Dependencies

- 003-client-management
- Map integration (future enhancement)
- File upload service for site plans

## Notes

- Site types should align with survey specializations from user-functionality-analysis.md
- GPS coordinates essential for mobile navigation
- Site plans upload prepares for future coordinate overlay feature
- Access instructions critical for solo workers in field