# Business Profiles Data Model

## Overview

The business profiles data model stores essential business information for each tenant organization in the Strata Compliance system. This forms the foundation for report generation, branding, and business identity management.

## Database Schema

### Table: `business_profiles`

The `business_profiles` table stores comprehensive business information for each tenant organization.

#### Core Structure

```sql
CREATE TABLE business_profiles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    
    -- Multi-tenancy
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    
    -- Core business information
    company_name TEXT NOT NULL,
    trading_name TEXT,
    company_number TEXT,
    vat_number TEXT,
    
    -- Contact information
    email TEXT,
    phone TEXT,
    mobile TEXT,
    website TEXT,
    
    -- Address information
    address_line_1 TEXT,
    address_line_2 TEXT,
    city TEXT,
    state_province TEXT,
    postal_code TEXT,
    country TEXT DEFAULT 'United Kingdom',
    
    -- Professional details
    industry TEXT,
    business_type TEXT,
    professional_body_registration TEXT,
    insurance_policy_number TEXT,
    insurance_expiry_date DATE,
    
    -- Branding
    logo_url TEXT,
    brand_color TEXT,
    
    -- Report settings
    report_footer_text TEXT,
    default_report_template TEXT DEFAULT 'standard',
    
    -- Business settings
    default_currency TEXT DEFAULT 'GBP',
    default_timezone TEXT DEFAULT 'Europe/London',
    financial_year_end DATE,
    
    -- Status and audit
    status TEXT DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'archived')),
    archived_at TIMESTAMPTZ,
    archived_by UUID REFERENCES auth.users(id)
);
```

#### Field Descriptions

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `id` | UUID | Yes | Primary key, auto-generated |
| `created_at` | TIMESTAMPTZ | Yes | Record creation timestamp |
| `updated_at` | TIMESTAMPTZ | Yes | Last update timestamp |
| `tenant_id` | UUID | Yes | Reference to tenant organization |
| `user_id` | UUID | Yes | User who created the profile |
| `company_name` | TEXT | Yes | Official registered company name |
| `trading_name` | TEXT | No | Trading name or DBA if different |
| `company_number` | TEXT | No | Companies House registration number (UK) |
| `vat_number` | TEXT | No | VAT registration number |
| `email` | TEXT | No | Primary business email address |
| `phone` | TEXT | No | Primary business phone number |
| `mobile` | TEXT | No | Mobile phone number |
| `website` | TEXT | No | Company website URL |
| `address_line_1` | TEXT | No | Primary address line |
| `address_line_2` | TEXT | No | Secondary address line |
| `city` | TEXT | No | City or town |
| `state_province` | TEXT | No | State, province, or county |
| `postal_code` | TEXT | No | Postal or ZIP code |
| `country` | TEXT | No | Country (defaults to 'United Kingdom') |
| `industry` | TEXT | No | Industry sector |
| `business_type` | TEXT | No | Business structure (e.g., 'limited_company', 'sole_trader') |
| `professional_body_registration` | TEXT | No | Professional body registration numbers |
| `insurance_policy_number` | TEXT | No | Professional indemnity insurance policy |
| `insurance_expiry_date` | DATE | No | Insurance policy expiry date |
| `logo_url` | TEXT | No | URL to company logo image |
| `brand_color` | TEXT | No | Primary brand color in hex format |
| `report_footer_text` | TEXT | No | Custom footer text for reports |
| `default_report_template` | TEXT | No | Default template for report generation |
| `default_currency` | TEXT | No | Default currency (defaults to 'GBP') |
| `default_timezone` | TEXT | No | Default timezone (defaults to 'Europe/London') |
| `financial_year_end` | DATE | No | Financial year end date |
| `status` | TEXT | No | Record status ('active', 'inactive', 'archived') |
| `archived_at` | TIMESTAMPTZ | No | Timestamp when record was archived |
| `archived_by` | UUID | No | User who archived the record |

## Constraints and Validation

### Primary Constraints

1. **Unique Tenant Profile**: Each tenant can have only one business profile
   ```sql
   UNIQUE(tenant_id)
   ```

2. **Email Validation**: Email addresses must follow standard format
   ```sql
   CONSTRAINT valid_email CHECK (email IS NULL OR email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$')
   ```

3. **Website URL Validation**: Website URLs must use HTTP/HTTPS protocol
   ```sql
   CONSTRAINT valid_website CHECK (website IS NULL OR website ~* '^https?://')
   ```

4. **Brand Color Validation**: Brand colors must be valid hex colors
   ```sql
   CONSTRAINT valid_brand_color CHECK (brand_color IS NULL OR brand_color ~* '^#[0-9A-Fa-f]{6}$')
   ```

5. **Status Validation**: Status must be one of the allowed values
   ```sql
   CHECK (status IN ('active', 'inactive', 'archived'))
   ```

### Foreign Key Relationships

- `tenant_id` → `tenants(id)` ON DELETE CASCADE
- `user_id` → `auth.users(id)` ON DELETE CASCADE  
- `archived_by` → `auth.users(id)`

## Row Level Security (RLS)

### Security Policies

1. **View Policy**: Users can view their own tenant's business profile
   ```sql
   CREATE POLICY "Users can view their tenant business profile"
       ON business_profiles FOR SELECT
       USING (tenant_id = (auth.jwt() ->> 'tenant_id')::uuid);
   ```

2. **Create Policy**: Only tenant admins can create business profiles
   ```sql
   CREATE POLICY "Tenant admins can create business profile"
       ON business_profiles FOR INSERT
       WITH CHECK (
           tenant_id = (auth.jwt() ->> 'tenant_id')::uuid 
           AND (auth.jwt() ->> 'role') = 'TenantAdmin'
       );
   ```

3. **Update Policy**: Only tenant admins can update business profiles
   ```sql
   CREATE POLICY "Tenant admins can update business profile"
       ON business_profiles FOR UPDATE
       USING (
           tenant_id = (auth.jwt() ->> 'tenant_id')::uuid 
           AND (auth.jwt() ->> 'role') = 'TenantAdmin'
       );
   ```

4. **Delete Policy**: Only tenant admins can delete business profiles
   ```sql
   CREATE POLICY "Tenant admins can delete business profile"
       ON business_profiles FOR DELETE
       USING (
           tenant_id = (auth.jwt() ->> 'tenant_id')::uuid 
           AND (auth.jwt() ->> 'role') = 'TenantAdmin'
       );
   ```

### Security Model

- **Multi-tenant Isolation**: All policies enforce tenant-based data isolation
- **Role-based Access**: Only TenantAdmin role can modify business profiles
- **JWT Claims**: Security relies on `tenant_id` and `role` claims in JWT tokens
- **Audit Trail**: All changes are tracked with timestamps and user references

## Performance Optimization

### Indexes

```sql
CREATE INDEX idx_business_profiles_tenant_id ON business_profiles(tenant_id);
CREATE INDEX idx_business_profiles_user_id ON business_profiles(user_id);
CREATE INDEX idx_business_profiles_status ON business_profiles(status);
CREATE INDEX idx_business_profiles_company_name ON business_profiles(company_name);
```

### Query Patterns

- **Primary Access**: By `tenant_id` (most common query pattern)
- **User Lookup**: By `user_id` for audit purposes
- **Status Filtering**: By `status` for active/archived filtering
- **Search**: By `company_name` for business lookup

## TypeScript Integration

### Core Interfaces

```typescript
export interface BusinessProfile {
  id: string;
  created_at: string;
  updated_at: string;
  tenant_id: string;
  user_id: string;
  company_name: string;
  trading_name?: string;
  // ... other fields
  status: EntityStatus;
}

export interface CreateBusinessProfileRequest {
  company_name: string;
  trading_name?: string;
  // ... other optional fields
}

export interface UpdateBusinessProfileRequest {
  company_name?: string;
  trading_name?: string;
  // ... all fields optional
  status?: EntityStatus;
}
```

### Usage Examples

```typescript
// Create a new business profile
const createRequest: CreateBusinessProfileRequest = {
  company_name: 'Strata Compliance Ltd',
  email: '<EMAIL>',
  brand_color: '#0066CC'
};

// Update existing profile
const updateRequest: UpdateBusinessProfileRequest = {
  phone: '+44 20 1234 5678',
  website: 'https://strata.com'
};
```

## Migration and Deployment

### Migration Script

- **File**: `database/migrations/006_create_business_profiles.sql`
- **Rollback**: `database/migrations/006_rollback_business_profiles.sql`

### Deployment Steps

1. Execute migration script in Supabase SQL editor
2. Verify RLS policies are enabled
3. Test with sample data
4. Update application types and interfaces
5. Deploy application code

## Business Rules

### Data Integrity

1. **One Profile Per Tenant**: Each tenant organization has exactly one business profile
2. **Required Information**: Only company name is mandatory, all other fields are optional
3. **UK Focus**: Default values optimized for UK businesses
4. **Professional Compliance**: Fields for professional body registrations and insurance

### Archiving

- **Soft Delete**: Records are archived, not physically deleted
- **Audit Trail**: Archiving user and timestamp are recorded
- **Status Management**: Archived profiles maintain data integrity

## Future Considerations

### Planned Enhancements

1. **Companies House Integration**: Automatic company data lookup
2. **Multi-location Support**: Support for businesses with multiple offices
3. **Document Storage**: Integration with document management system
4. **Compliance Tracking**: Professional certification and renewal tracking

### Scalability

- **Read Replicas**: Consider read replicas for reporting queries
- **Caching**: Business profile data is ideal for caching
- **API Rate Limiting**: Protect against excessive profile updates
