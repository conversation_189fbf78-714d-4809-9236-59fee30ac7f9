# Task 004-001: Create Site Data Model and Schema

**Parent Use Case:** 004-site-management  
**Task ID:** 004-001  
**Title:** Create Site Data Model and Schema  
**Estimated Development Time:** 2-3 hours (including tests/docs)  
**Estimated Agent Time:** 2-3 minutes  

## Description

Define and implement the site data structure in Supabase and create corresponding TypeScript interfaces. This forms the foundation for all site management functionality, establishing the database schema, RLS policies, and type definitions with proper relationships to clients and future inspections.

## Acceptance Criteria

- [ ] Site table exists in Supabase with all required fields
- [ ] Key holder contacts table exists with proper relationships
- [ ] Health & safety requirements table exists 
- [ ] RLS policies implemented for multi-tenant data isolation
- [ ] TypeScript interfaces defined and exported
- [ ] Database migration scripts created and documented
- [ ] Table relationships properly defined with foreign keys
- [ ] Data validation rules implemented at database level
- [ ] GPS coordinate storage and validation
- [ ] UK address format support and validation

## Deliverables Checklist

### Database Schema
- [ ] Create `sites` table in Supabase
- [ ] Add required columns: id, tenant_id, client_id, name, address fields, gps_lat, gps_lng, site_type, etc.
- [ ] Create `site_key_holders` table for key holder contacts
- [ ] Create `site_health_safety` table for H&S requirements
- [ ] Implement proper data types and constraints
- [ ] Add created_at, updated_at timestamps
- [ ] Create indexes for performance optimization

### Security & Access Control
- [ ] Implement RLS policies for tenant isolation on sites table
- [ ] Implement RLS policies for site_key_holders table
- [ ] Implement RLS policies for site_health_safety table
- [ ] Create policy for users to read their tenant's sites
- [ ] Create policy for users to manage their tenant's sites
- [ ] Test RLS policies with different user scenarios

### TypeScript Interfaces
- [ ] Define Site interface with all fields
- [ ] Define SiteKeyHolder interface for key holder contacts
- [ ] Define SiteHealthSafety interface for H&S requirements
- [ ] Define CreateSiteRequest interface
- [ ] Define UpdateSiteRequest interface
- [ ] Define SiteType enum for UK property types
- [ ] Export interfaces from shared types package

### Geographic Data Support
- [ ] Add GPS coordinate fields (latitude, longitude)
- [ ] Implement coordinate validation rules
- [ ] Add location accuracy and timestamp fields
- [ ] Create geographic indexes for location queries
- [ ] Add address geocoding support preparation
- [ ] Implement UK postcode validation and lookup

### Migration & Documentation
- [ ] Create database migration script for sites table
- [ ] Create database migration script for related tables
- [ ] Create rollback migration scripts
- [ ] Document table structure and field purposes
- [ ] Document RLS policy logic and security model
- [ ] Document geographic data handling

### Testing
- [ ] Write unit tests for TypeScript interface validation
- [ ] Test database constraints and validation rules
- [ ] Test RLS policies with multiple tenants
- [ ] Verify migration scripts work correctly
- [ ] Test foreign key relationships with clients
- [ ] Test GPS coordinate validation and storage

## Dependencies

- **Required Before Starting:**
  - 003-client-management (client data model for foreign key relationships)

- **Blocks These Tasks:**
  - 004-002 (Core Site Form Component)
  - 004-010 (Site API Endpoints)
  - All other site management tasks

## Technical Considerations

- **Multi-tenant Architecture**: All site data must be isolated by tenant_id using RLS policies
- **Client Relationships**: Proper foreign key constraints to clients table with cascade handling
- **Geographic Data**: GPS coordinates stored with appropriate precision for mobile accuracy
- **UK-Specific Requirements**: Address format and postcode validation for UK properties
- **Mobile Optimization**: Database queries optimized for mobile performance and offline sync
- **Data Relationships**: Sites serve as parent for inspections - foreign key constraints essential

## User Experience Considerations

- **Address Validation**: Support for UK address formats and postcode lookup
- **GPS Integration**: Coordinate capture from mobile device GPS
- **Site Classification**: Property types aligned with UK surveying practices
- **Accessibility**: Address and location data accessible for users with disabilities

## Integration Points

- **Client Management**: Foreign key relationship with clients table
- **Inspection Workflow**: Foundation for site-based inspections
- **Geographic Services**: GPS and mapping service integration points
- **Address Services**: UK postcode and address lookup integration

## Definition of Done

- [ ] All database tables created with proper constraints
- [ ] RLS policies tested and verified secure
- [ ] TypeScript interfaces exported and documented
- [ ] Migration scripts tested in development environment
- [ ] Unit tests passing with 100% coverage
- [ ] Geographic data handling tested and validated
- [ ] Client relationship constraints working correctly
- [ ] Ready for API endpoint development and form integration