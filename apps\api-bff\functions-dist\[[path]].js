import { DatabaseService } from "@strata/core";
import { createClient } from "@supabase/supabase-js";
import { Hono } from "hono";
import { cors } from "hono/cors";
// Initialize Hono app
const app = new Hono();
// CORS configuration for mobile app
app.use("*", cors({
    origin: [
        "http://localhost:8100",
        "capacitor://localhost",
        "ionic://localhost",
    ],
    allowHeaders: ["Content-Type", "Authorization"],
    allowMethods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
}));
// Initialize Supabase client with service role (server-side only)
const supabaseUrl = "https://fwktrittbrmqarkipcpz.supabase.co";
const supabaseServiceKey = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ3a3RyaXR0YnJtcWFya2lwY3B6Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MDE4OTE5NSwiZXhwIjoyMDY1NzY1MTk1fQ.ldI65o8yJCY76i2LRi8eZA_ljv2peuomIyA9sucAg0M";
const supabase = createClient(supabaseUrl, supabaseServiceKey);
const db = new DatabaseService(supabase);
// Middleware to verify JWT and extract user info
const authMiddleware = async (c, next) => {
    const authHeader = c.req.header("Authorization");
    if (!authHeader || !authHeader.startsWith("Bearer ")) {
        return c.json({ error: "Missing or invalid authorization header" }, 401);
    }
    const token = authHeader.substring(7);
    try {
        // Verify JWT with Supabase
        const { data: { user }, error, } = await supabase.auth.getUser(token);
        if (error || !user) {
            return c.json({ error: "Invalid token" }, 401);
        }
        // Get user's tenant info
        const tenantInfo = await db.getUserTenantInfo(user.id);
        if (!tenantInfo) {
            return c.json({ error: "User not associated with any tenant" }, 403);
        }
        // Add user and tenant info to context
        c.set("user", user);
        c.set("tenantInfo", tenantInfo);
        await next();
    }
    catch (error) {
        return c.json({ error: "Authentication failed" }, 401);
    }
};
// Health check endpoint
app.get("/", (c) => {
    return c.json({
        message: "Strata Compliance API",
        version: "1.0.0",
        timestamp: new Date().toISOString(),
    });
});
// Authentication endpoints
app.post("/auth/signin", async (c) => {
    const { email, password } = await c.req.json();
    const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
    });
    if (error) {
        return c.json({ error: error.message }, 400);
    }
    return c.json({
        user: data.user,
        session: data.session,
    });
});
app.post("/auth/signup", async (c) => {
    const { email, password } = await c.req.json();
    const { data, error } = await supabase.auth.signUp({
        email,
        password,
    });
    if (error) {
        return c.json({ error: error.message }, 400);
    }
    return c.json({
        user: data.user,
        session: data.session,
    });
});
// Protected routes - require authentication
app.use("/api/*", authMiddleware);
// Tenant endpoints
app.get("/api/tenant", async (c) => {
    const tenantInfo = c.get("tenantInfo");
    const tenant = await db.getTenant(tenantInfo.tenant_id);
    return c.json({ tenant });
});
// Client endpoints
app.get("/api/clients", async (c) => {
    const tenantInfo = c.get("tenantInfo");
    const clients = await db.getClients(tenantInfo.tenant_id);
    return c.json({ clients });
});
app.post("/api/clients", async (c) => {
    const tenantInfo = c.get("tenantInfo");
    const clientData = await c.req.json();
    // Ensure tenant_id is set correctly
    clientData.tenant_id = tenantInfo.tenant_id;
    const client = await db.createClient(clientData);
    return c.json({ client });
});
// Site endpoints
app.get("/api/sites", async (c) => {
    const tenantInfo = c.get("tenantInfo");
    // Verify tenant is active
    const tenant = await db.getTenant(tenantInfo.tenant_id);
    if (!tenant || tenant.status !== "active") {
        return c.json({ error: "Tenant is not active" }, 403);
    }
    const sites = await db.getSitesForTenant(tenantInfo.tenant_id);
    return c.json({ sites });
});
// Export for Cloudflare Pages Functions
export default {
    fetch: app.fetch,
};
