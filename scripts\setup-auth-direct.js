#!/usr/bin/env node

/**
 * Direct Authentication Setup using Supabase Service Role
 * This script configures RLS policies and tests authentication without touching auth schema
 */

import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://fwktrittbrmqarkipcpz.supabase.co';
const serviceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ3a3RyaXR0YnJtcWFya2lwY3B6Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MDE4OTE5NSwiZXhwIjoyMDY1NzY1MTk1fQ.ldI65o8yJCY76i2LRi8eZA_ljv2peuomIyA9sucAg0M';

const supabase = createClient(supabaseUrl, serviceKey);

async function enableRLS() {
  console.log('🔒 Enabling Row Level Security...');
  
  const tables = ['tenants', 'tenant_users', 'clients', 'properties', 'sites', 'inspections'];
  
  for (const table of tables) {
    try {
      console.log(`   🔄 Enabling RLS for ${table}...`);
      
      // Use direct SQL execution via service role
      const { error } = await supabase.sql`
        ALTER TABLE ${supabase.sql(table)} ENABLE ROW LEVEL SECURITY;
      `;
      
      if (error) {
        console.log(`   ❌ Error enabling RLS for ${table}: ${error.message}`);
      } else {
        console.log(`   ✅ RLS enabled for ${table}`);
      }
      
    } catch (error) {
      console.log(`   ❌ Error enabling RLS for ${table}: ${error.message}`);
    }
  }
}

async function createRLSPolicies() {
  console.log('\n📋 Creating RLS Policies...');
  
  const policies = [
    // Tenants policies
    {
      table: 'tenants',
      name: 'Users can view their own tenant',
      operation: 'SELECT',
      sql: `CREATE POLICY "Users can view their own tenant" ON tenants FOR SELECT USING (id = (auth.jwt() ->> 'tenant_id')::uuid);`
    },
    {
      table: 'tenants', 
      name: 'Only tenant admins can update tenant details',
      operation: 'UPDATE',
      sql: `CREATE POLICY "Only tenant admins can update tenant details" ON tenants FOR UPDATE USING (id = (auth.jwt() ->> 'tenant_id')::uuid AND (auth.jwt() ->> 'role') = 'TenantAdmin');`
    },
    
    // Tenant users policies
    {
      table: 'tenant_users',
      name: 'Users can view their own tenant memberships',
      operation: 'SELECT', 
      sql: `CREATE POLICY "Users can view their own tenant memberships" ON tenant_users FOR SELECT USING (tenant_id = (auth.jwt() ->> 'tenant_id')::uuid);`
    },
    {
      table: 'tenant_users',
      name: 'Tenant admins can manage tenant users',
      operation: 'ALL',
      sql: `CREATE POLICY "Tenant admins can manage tenant users" ON tenant_users FOR ALL USING (tenant_id = (auth.jwt() ->> 'tenant_id')::uuid AND (auth.jwt() ->> 'role') = 'TenantAdmin');`
    },
    
    // Clients policies
    {
      table: 'clients',
      name: 'Users can view clients in their tenant',
      operation: 'SELECT',
      sql: `CREATE POLICY "Users can view clients in their tenant" ON clients FOR SELECT USING (tenant_id = (auth.jwt() ->> 'tenant_id')::uuid);`
    },
    {
      table: 'clients',
      name: 'Schedulers and admins can manage clients',
      operation: 'ALL',
      sql: `CREATE POLICY "Schedulers and admins can manage clients" ON clients FOR ALL USING (tenant_id = (auth.jwt() ->> 'tenant_id')::uuid AND (auth.jwt() ->> 'role') IN ('TenantAdmin', 'Scheduler'));`
    }
  ];
  
  for (const policy of policies) {
    try {
      console.log(`   🔄 Creating policy: ${policy.name}...`);
      
      // Drop existing policy first
      await supabase.sql`DROP POLICY IF EXISTS ${supabase.sql(policy.name)} ON ${supabase.sql(policy.table)};`;
      
      // Create new policy
      const { error } = await supabase.sql([policy.sql]);
      
      if (error) {
        console.log(`   ❌ Error creating policy ${policy.name}: ${error.message}`);
      } else {
        console.log(`   ✅ Created policy: ${policy.name}`);
      }
      
    } catch (error) {
      console.log(`   ❌ Error with policy ${policy.name}: ${error.message}`);
    }
  }
}

async function createUserManagementFunctions() {
  console.log('\n🔧 Creating User Management Functions...');
  
  try {
    // Function to get user's tenant info
    const getUserTenantInfoSQL = `
      CREATE OR REPLACE FUNCTION get_user_tenant_info(user_id uuid)
      RETURNS TABLE(tenant_id uuid, role text, first_name text, last_name text)
      LANGUAGE plpgsql
      SECURITY DEFINER
      AS $$
      BEGIN
        RETURN QUERY
        SELECT tu.tenant_id, tu.role, tu.first_name, tu.last_name
        FROM tenant_users tu
        WHERE tu.user_id = get_user_tenant_info.user_id
        AND tu.status = 'active'
        LIMIT 1;
      END;
      $$;
    `;
    
    const { error: funcError } = await supabase.sql([getUserTenantInfoSQL]);
    
    if (funcError) {
      console.log(`   ❌ Error creating user tenant info function: ${funcError.message}`);
    } else {
      console.log(`   ✅ Created get_user_tenant_info function`);
    }
    
    // Function to add user to tenant
    const addUserToTenantSQL = `
      CREATE OR REPLACE FUNCTION add_user_to_tenant(
        p_tenant_id uuid,
        p_user_id uuid,
        p_role text,
        p_first_name text DEFAULT NULL,
        p_last_name text DEFAULT NULL
      )
      RETURNS uuid
      LANGUAGE plpgsql
      SECURITY DEFINER
      AS $$
      DECLARE
        new_id uuid;
      BEGIN
        INSERT INTO tenant_users (tenant_id, user_id, role, first_name, last_name, status)
        VALUES (p_tenant_id, p_user_id, p_role, p_first_name, p_last_name, 'active')
        RETURNING id INTO new_id;
        
        RETURN new_id;
      END;
      $$;
    `;
    
    const { error: addUserError } = await supabase.sql([addUserToTenantSQL]);
    
    if (addUserError) {
      console.log(`   ❌ Error creating add_user_to_tenant function: ${addUserError.message}`);
    } else {
      console.log(`   ✅ Created add_user_to_tenant function`);
    }
    
  } catch (error) {
    console.log(`   ❌ Error creating functions: ${error.message}`);
  }
}

async function testRLSEnforcement() {
  console.log('\n🧪 Testing RLS Enforcement...');
  
  const anonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ3a3RyaXR0YnJtcWFya2lwY3B6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAxODkxOTUsImV4cCI6MjA2NTc2NTE5NX0.cbfaZXV4NwBwRXVSRF0xSzWVUTmfXlwZa4ptuOF0se0';
  const anonClient = createClient(supabaseUrl, anonKey);
  
  const tables = ['tenants', 'tenant_users', 'clients'];
  
  for (const table of tables) {
    try {
      const { data, error } = await anonClient.from(table).select('*').limit(1);
      
      if (error) {
        if (error.message.includes('RLS') || error.message.includes('policy') || error.message.includes('permission')) {
          console.log(`   ✅ ${table}: RLS working (${error.message})`);
        } else {
          console.log(`   ⚠️  ${table}: Unexpected error (${error.message})`);
        }
      } else {
        console.log(`   ❌ ${table}: RLS not enforced (returned ${data?.length || 0} rows)`);
      }
    } catch (e) {
      console.log(`   ✅ ${table}: RLS working (access denied)`);
    }
  }
}

async function verifySetup() {
  console.log('\n📊 Verifying Setup...');
  
  try {
    // Check RLS status
    const { data: rlsData, error: rlsError } = await supabase
      .from('pg_tables')
      .select('tablename, rowsecurity')
      .eq('schemaname', 'public')
      .in('tablename', ['tenants', 'tenant_users', 'clients', 'properties', 'sites', 'inspections']);
    
    if (rlsError) {
      console.log(`   ❌ Error checking RLS status: ${rlsError.message}`);
    } else {
      console.log('   📋 RLS Status:');
      rlsData?.forEach(table => {
        console.log(`      ${table.tablename}: ${table.rowsecurity ? '✅ Enabled' : '❌ Disabled'}`);
      });
    }
    
    // Check policies
    const { data: policiesData, error: policiesError } = await supabase
      .from('pg_policies')
      .select('tablename, policyname, cmd')
      .eq('schemaname', 'public');
    
    if (policiesError) {
      console.log(`   ❌ Error checking policies: ${policiesError.message}`);
    } else {
      console.log(`   📋 Found ${policiesData?.length || 0} RLS policies`);
    }
    
  } catch (error) {
    console.log(`   ❌ Error verifying setup: ${error.message}`);
  }
}

async function main() {
  console.log('🚀 Direct Authentication Setup');
  console.log('==============================\n');
  
  // Enable RLS
  await enableRLS();
  
  // Create RLS policies
  await createRLSPolicies();
  
  // Create helper functions
  await createUserManagementFunctions();
  
  // Test RLS enforcement
  await testRLSEnforcement();
  
  // Verify setup
  await verifySetup();
  
  console.log('\n🎉 Authentication setup complete!');
  console.log('\n📋 Next Steps:');
  console.log('1. JWT claims still need to be configured in Supabase Dashboard');
  console.log('2. Go to Authentication > Hooks and configure custom claims');
  console.log('3. Test user signup and tenant association');
  console.log('4. Use the helper functions to manage users');
}

main().catch(console.error);
