# API Implementation Guide

## 🔒 Secure API-First Architecture

Strata Compliance uses a secure API-first architecture where mobile apps communicate with a Cloudflare Workers API layer instead of directly accessing the database.

## 🏗️ Architecture Overview

```
Mobile App → API (Cloudflare Workers) → Supabase → Database
```

**Benefits:**
- Database credentials never exposed to client applications
- Centralized authentication and authorization
- Built-in security controls and audit trails
- Scalable and performant edge computing

## 🚀 Getting Started

### 1. Start the API Server

```bash
cd apps/api-bff
pnpm install
pnpm dev
```

The API will be available at `http://localhost:8787`

### 2. Configure Mobile App

```bash
# In apps/mobile/.env.local
VITE_API_URL=http://localhost:8787
```

### 3. Test the API

```bash
# Health check
curl http://localhost:8787/

# Authentication
curl -X POST http://localhost:8787/auth/signin \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password"}'
```

## 📱 Mobile App Usage

### Authentication

```typescript
import { useSecureAuth } from '@strata/auth';

const LoginComponent = () => {
  const { signInWithEmail, signUp, user, loading, error } = useSecureAuth();

  const handleSignIn = async (email: string, password: string) => {
    const error = await signInWithEmail(email, password);
    if (error) {
      console.error('Sign in failed:', error.message);
    }
  };

  return (
    // Your login UI
  );
};
```

### Data Access

```typescript
import { useApiClient } from '@strata/core';
import { useSecureAuth } from '@strata/auth';

const ClientsComponent = () => {
  const { tenantInfo } = useSecureAuth();
  const api = useApiClient();
  const [clients, setClients] = useState([]);
  const [error, setError] = useState(null);

  useEffect(() => {
    const loadClients = async () => {
      const { data, error } = await api.getClients();
      if (error) {
        setError(error);
      } else {
        setClients(data);
      }
    };

    if (tenantInfo?.tenantId) {
      loadClients();
    }
  }, [tenantInfo]);

  const createClient = async (clientData) => {
    const { data, error } = await api.createClient(clientData);
    if (error) {
      setError(error);
    } else {
      setClients([...clients, data]);
    }
  };

  return (
    // Your clients UI
  );
};
```

## 🔧 API Endpoints

### Authentication Endpoints

- `POST /auth/signin` - Sign in with email/password
- `POST /auth/signup` - Create new user account

### Protected Endpoints (require JWT token)

- `GET /api/tenant` - Get current user's tenant information
- `GET /api/clients` - Get clients for current tenant
- `POST /api/clients` - Create new client

### Request/Response Format

**Authentication Request:**
```json
{
  "email": "<EMAIL>",
  "password": "password"
}
```

**Authentication Response:**
```json
{
  "user": {
    "id": "uuid",
    "email": "<EMAIL>"
  },
  "session": {
    "access_token": "jwt-token",
    "expires_at": "2024-01-01T00:00:00Z"
  }
}
```

**API Response Format:**
```json
{
  "data": { /* response data */ },
  "error": "error message if any"
}
```

## 🔒 Security Features

### JWT Authentication

All protected endpoints require a valid JWT token in the Authorization header:

```
Authorization: Bearer <jwt-token>
```

### Automatic Tenant Context

The API automatically:
- Verifies JWT tokens
- Extracts user information
- Loads tenant context
- Enforces tenant isolation

### CORS Configuration

The API is configured to accept requests from:
- `http://localhost:8100` (Ionic dev server)
- `capacitor://localhost` (iOS app)
- `ionic://localhost` (Android app)

## 🧪 Testing

### Unit Tests

```bash
cd apps/api-bff
pnpm test
```

### Integration Tests

```bash
# Test authentication
curl -X POST http://localhost:8787/auth/signin \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password"}'

# Test protected endpoint
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  http://localhost:8787/api/clients
```

### Security Tests

1. **Verify no database credentials in mobile app:**
   ```bash
   cd apps/mobile
   pnpm build
   grep -r "supabase" dist/  # Should not find database URLs/keys
   ```

2. **Test authentication is required:**
   ```bash
   # Should return 401
   curl http://localhost:8787/api/clients
   ```

3. **Test tenant isolation:**
   - Sign in as different users
   - Verify they only see their tenant's data

## 🚀 Deployment

### Deploy API to Cloudflare

```bash
cd apps/api-bff
pnpm deploy
```

### Update Mobile App Configuration

```bash
# For production
VITE_API_URL=https://your-api.pages.dev
```

## 📋 Development Checklist

### API Development
- [ ] API server starts successfully
- [ ] Authentication endpoints work
- [ ] Protected endpoints require valid JWT
- [ ] Tenant isolation is enforced
- [ ] Error handling is comprehensive

### Mobile App Development
- [ ] Uses `useSecureAuth` for authentication
- [ ] Uses `useApiClient` for data access
- [ ] Handles API response format correctly
- [ ] No database credentials in app bundle
- [ ] Error states are handled gracefully

### Security Verification
- [ ] Database credentials only on server
- [ ] JWT tokens are verified on all protected endpoints
- [ ] Users can only access their tenant's data
- [ ] CORS is properly configured
- [ ] All requests are logged for audit

## 🔗 Related Documentation

- [Security Architecture](./security-architecture.md) - Detailed security overview
- [Authentication Setup Guide](./authentication-setup-guide.md) - Database and auth configuration
- [Database Implementation Summary](./database-implementation-summary.md) - Schema overview

This API-first architecture ensures that Strata Compliance maintains the highest security standards while providing a scalable and maintainable foundation for the mobile application.
