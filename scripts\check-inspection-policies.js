#!/usr/bin/env node

import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://fwktrittbrmqarkipcpz.supabase.co';
const serviceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ3a3RyaXR0YnJtcWFya2lwY3B6Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MDE4OTE5NSwiZXhwIjoyMDY1NzY1MTk1fQ.ldI65o8yJCY76i2LRi8eZA_ljv2peuomIyA9sucAg0M';

const supabase = createClient(supabaseUrl, serviceKey);

async function checkInspectionPolicies() {
  console.log('🔍 Checking Inspection Table Policies...\n');
  
  try {
    // Use a direct SQL query to get policy information
    const { data, error } = await supabase.rpc('sql', {
      query: `
        SELECT 
          policyname,
          cmd,
          qual,
          with_check
        FROM pg_policies 
        WHERE schemaname = 'public' 
        AND tablename = 'inspections'
        ORDER BY policyname;
      `
    });
    
    if (error) {
      console.log('❌ Error fetching policies:', error.message);
      console.log('Trying alternative approach...\n');
      
      // Alternative: Check what we can see from the RLS status
      const { data: rlsData, error: rlsError } = await supabase.rpc('check_rls_status');
      
      if (rlsError) {
        console.log('❌ Cannot access RLS status:', rlsError.message);
        return;
      }
      
      const inspectionTable = rlsData.find(table => table.table_name === 'inspections');
      if (inspectionTable) {
        console.log(`📊 Inspections table has ${inspectionTable.policy_count} policies`);
        console.log('\nExpected policies:');
        console.log('1. "Users can view inspections in their tenant"');
        console.log('2. "Inspectors can view and update their assigned inspections"');
        console.log('3. "Inspectors can create inspections in their tenant"');
        console.log('4. "Schedulers and admins can manage all inspections"');
        
        if (inspectionTable.policy_count > 4) {
          console.log('\n⚠️  More policies than expected - there might be duplicate/old policies');
        }
      }
      
    } else {
      console.log('📋 Current Inspection Policies:');
      data?.forEach((policy, index) => {
        console.log(`\n${index + 1}. Policy: "${policy.policyname}"`);
        console.log(`   Command: ${policy.cmd}`);
        console.log(`   Using: ${policy.qual || 'N/A'}`);
        console.log(`   With Check: ${policy.with_check || 'N/A'}`);
      });
      
      console.log(`\n📊 Total policies found: ${data?.length || 0}`);
      
      // Check for the old problematic policy
      const oldPolicy = data?.find(p => p.policyname === 'Inspectors can manage their assigned inspections');
      if (oldPolicy) {
        console.log('\n⚠️  Found old policy "Inspectors can manage their assigned inspections"');
        console.log('   This policy should be removed as it conflicts with the new split policies');
      }
    }
    
  } catch (e) {
    console.log('❌ Error:', e.message);
  }
}

checkInspectionPolicies().catch(console.error);
