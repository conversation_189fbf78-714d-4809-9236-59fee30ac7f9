import React from 'react';
import { Redirect, Route } from 'react-router-dom';
import type { User } from '@strata/core';
import AppLoading from './AppLoading';

interface ProtectedRouteProps {
  path: string;
  component: React.ComponentType<any>;
  user: User | null;
  loading: boolean;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ 
  path, 
  component: Component, 
  user, 
  loading 
}) => {
  return (
    <Route
      path={path}
      render={() => {
        if (loading) {
          return <AppLoading />;
        }
        
        return user ? <Component /> : <Redirect to="/auth" />;
      }}
    />
  );
};

export default ProtectedRoute;
