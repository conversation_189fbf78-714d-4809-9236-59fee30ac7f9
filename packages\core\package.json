{"name": "@strata/core", "version": "0.0.1", "private": true, "main": "./dist/index.js", "types": "./dist/index.d.ts", "scripts": {"build": "tsup src/index.ts", "dev": "tsup src/index.ts --watch", "lint": "eslint src/ test/", "test": "vitest run"}, "dependencies": {"@supabase/supabase-js": "^2.50.0"}, "devDependencies": {"@types/node": "^20.11.19", "eslint": "^8.56.0", "tsup": "^8.0.2", "typescript": "^5.3.3", "vitest": "^1.2.2"}}