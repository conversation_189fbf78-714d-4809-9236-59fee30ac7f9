import { createClient } from "@supabase/supabase-js";
// Browser-safe environment variable access
function getEnvVar(name) {
    // Check if we're in a browser environment
    if (typeof window !== "undefined") {
        // In browser, check for Vite env vars on import.meta.env
        if (typeof import.meta !== "undefined" && import.meta.env) {
            return import.meta.env[name] || "";
        }
        // Fallback to empty string in browser if import.meta.env not available
        return "";
    }
    // In Node.js environment, use process.env
    if (typeof process !== "undefined" && process.env) {
        return process.env[name] || "";
    }
    return "";
}
const supabaseUrl = getEnvVar("NEXT_PUBLIC_SUPABASE_URL") || getEnvVar("VITE_SUPABASE_URL") || "";
const supabaseAnonKey = getEnvVar("NEXT_PUBLIC_SUPABASE_ANON_KEY") ||
    getEnvVar("VITE_SUPABASE_ANON_KEY") ||
    "";
console.log("🔧 Supabase client config:", {
    url: supabaseUrl ? "configured" : "missing",
    key: supabaseAnonKey ? "configured" : "missing",
    urlLength: supabaseUrl.length,
    keyLength: supabaseAnonKey.length,
});
export const supabase = createClient(supabaseUrl, supabaseAnonKey);
//# sourceMappingURL=supabaseClient.js.map