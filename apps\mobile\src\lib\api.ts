// API utilities for mobile app
import { apiClient } from "@strata/core";
import { debugApi } from "./logging";

export const useApi = () => {
  const getSites = async () => {
    try {
      const response = await apiClient.getSites();
      if (response.error) {
        throw new Error(response.error);
      }
      return response.data || [];
    } catch (error) {
      debugApi("Failed to fetch sites", {
        error: error instanceof Error ? error.message : error,
      });
      throw error;
    }
  };

  return {
    getSites,
  };
};
