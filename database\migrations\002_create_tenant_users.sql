-- Create tenant_users junction table for multi-tenant access
CREATE TABLE tenant_users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    
    -- Relationships
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    
    -- Role and permissions
    role TEXT NOT NULL CHECK (role IN ('TenantAdmin', 'Scheduler', 'Inspector', 'ClientUser')),
    permissions JSONB DEFAULT '{}',
    
    -- Profile info (extends Supabase auth.users)
    first_name TEXT,
    last_name TEXT,
    position TEXT,
    office_phone TEXT,
    mobile_phone TEXT,
    signature_url TEXT,
    profile_image_url TEXT,
    
    -- Status
    status TEXT DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'suspended')),
    invited_at TIMESTAMPTZ,
    activated_at TIMESTAMPTZ,
    last_login_at TIMESTAMPTZ,
    
    -- Unique constraint: one role per user per tenant
    UNIQUE(tenant_id, user_id)
);

-- RLS policies
ALTER TABLE tenant_users ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own tenant memberships"
    ON tenant_users FOR SELECT
    USING (tenant_id = (auth.jwt() ->> 'tenant_id')::uuid);

CREATE POLICY "Tenant admins can manage tenant users"
    ON tenant_users FOR ALL
    USING (
        tenant_id = (auth.jwt() ->> 'tenant_id')::uuid 
        AND (auth.jwt() ->> 'role') = 'TenantAdmin'
    );

CREATE POLICY "Users can update their own profile"
    ON tenant_users FOR UPDATE
    USING (
        tenant_id = (auth.jwt() ->> 'tenant_id')::uuid 
        AND user_id = auth.uid()
    )
    WITH CHECK (
        -- Prevent users from changing their own role
        role = (SELECT role FROM tenant_users WHERE tenant_id = (auth.jwt() ->> 'tenant_id')::uuid AND user_id = auth.uid())
    );

-- Indexes
CREATE INDEX idx_tenant_users_tenant_id ON tenant_users(tenant_id);
CREATE INDEX idx_tenant_users_user_id ON tenant_users(user_id);
CREATE INDEX idx_tenant_users_role ON tenant_users(role);
CREATE INDEX idx_tenant_users_status ON tenant_users(status);

-- Updated_at trigger
CREATE TRIGGER update_tenant_users_updated_at 
    BEFORE UPDATE ON tenant_users 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column(); 