# Strata Compliance

A modern compliance management system built with TypeScript, React, and Cloudflare Workers.

## Project Structure

```
apps/
  mobile/      # Ionic React mobile app
  desktop/     # React + MUI desktop app
  api-bff/     # Cloudflare Workers API
packages/
  core/        # Shared domain logic
  ui/          # Shared UI components
  auth/        # Supabase client wrapper
```

## Getting Started

### Quick Start

```bash
# 1. Prerequisites: Node.js 18+, pnpm 8.15.4+
# 2. Clone and install
git clone https://github.com/stratacompliance/strata.git
cd strata
pnpm install

# 3. Set up environment (copy .env.example files)
# 4. Set up database (see database/SETUP.md)
# 5. Start development
pnpm dev
```

### Complete Setup Guide

For detailed setup instructions, see:

- **[Development Setup Guide](docs/development-setup.md)** - Complete environment setup
- **[Developer Onboarding Checklist](docs/developer-onboarding-checklist.md)** - Step-by-step verification
- **[Database Setup](database/SETUP.md)** - Database configuration and migrations

## Development

- Run tests: `pnpm test` (runs all tests once and exits)
- Run linting: `pnpm lint`
- Build all packages: `pnpm build`

> To run tests in watch mode for a specific package (for local development):
> `pnpm --filter <package> vitest --watch`

## Deployment

1. Build the apps:

   ```bash
   pnpm build
   ```

2. Deploy to Cloudflare Pages:
   ```bash
   pnpm --filter @strata/api-bff deploy
   ```

## Contributing

1. Create a new branch
2. Make your changes
3. Run tests and linting
4. Submit a pull request

## License

Private - All rights reserved
