-- Create clients table (adapted from tblClients)
-- Represents strata companies/organizations that are clients of the tenant
CREATE TABLE clients (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    
    -- Multi-tenant
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    
    -- Basic info (from original tblClients)
    name TEXT NOT NULL,
    url_safe_name TEXT, -- for URL generation
    logo_url TEXT,
    client_type INTEGER DEFAULT 2 NOT NULL, -- From original schema
    
    -- Contact details
    emergency_contact_details TEXT,
    escalation_procedure TEXT,
    kpis_and_slas TEXT,
    invoice_email_address TEXT,
    account_queries_email_address TEXT,
    general_requirements TEXT,
    
    -- Address components (from original)
    building_name TEXT,
    building_number TEXT,
    unit TEXT,
    floor TEXT,
    
    -- Status
    status TEXT DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'archived')),
    archived_at TIMESTAMPTZ,
    archived_by UUID REFERENCES auth.users(id),
    archive_reason TEXT
);

-- RLS policies
ALTER TABLE clients ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view clients in their tenant"
    ON clients FOR SELECT
    USING (tenant_id = (auth.jwt() ->> 'tenant_id')::uuid);

CREATE POLICY "Schedulers and admins can manage clients"
    ON clients FOR ALL
    USING (
        tenant_id = (auth.jwt() ->> 'tenant_id')::uuid 
        AND (auth.jwt() ->> 'role') IN ('TenantAdmin', 'Scheduler')
    );

-- Indexes
CREATE INDEX idx_clients_tenant_id ON clients(tenant_id);
CREATE INDEX idx_clients_name ON clients(name);
CREATE INDEX idx_clients_status ON clients(status);
CREATE INDEX idx_clients_url_safe_name ON clients(url_safe_name);

-- Updated_at trigger
CREATE TRIGGER update_clients_updated_at 
    BEFORE UPDATE ON clients 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column(); 