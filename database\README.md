# Database Schema Overview

Multi-tenant database schema for Strata Compliance, adapted from the existing HCA system.

## 🚀 Quick Start

**Ready to set up your database?** → See **[SETUP.md](./SETUP.md)** for complete instructions.

**Want implementation details?** → See **[Database Implementation Summary](../docs/database-implementation-summary.md)** for comprehensive overview.

## 📋 Architecture Summary

- **Source**: Adapted from `C:\Strata\Source\HCA\Hca.Database\dbo` (SQL Server → PostgreSQL)
- **Multi-tenant**: All tables include `tenant_id` with Row Level Security (RLS)
- **Hierarchy**: `Tenants → Clients → Sites → Properties → Inspections`
- **Auth**: Integrated with Supabase Auth + custom JWT claims

## 📁 Directory Structure

```
database/
├── SETUP.md                    # 👈 Complete setup instructions
├── migrations/                 # SQL migration files (run in order)
│   ├── 001_create_tenants.sql
│   ├── 002_create_tenant_users.sql
│   ├── 003_create_clients.sql
│   ├── 004_create_properties.sql
│   └── 005_create_sites_and_inspections.sql
└── reference/                  # Original HCA schema for reference
    └── tables/
```

## 🔧 Testing & Scripts

- **Test Connection**: `cd scripts && node test-db.js`
- **Automated Setup**: `cd scripts && node setup-staging.js`
- **TypeScript Types**: Available in `packages/core/src/types/`
