/**
 * Test data fixtures for Strata Compliance authentication tests
 */

export const testUsers = {
  validUser: {
    email: '<EMAIL>',
    password: 'SecurePass123!',
    businessName: 'Test Survey Business',
    firstName: 'Test',
    lastName: 'Surveyor'
  },
  
  existingUser: {
    email: '<EMAIL>', 
    password: 'ExistingPass123!',
    businessName: 'Existing Business'
  },

  weakPasswordUser: {
    email: '<EMAIL>',
    password: '123',
    businessName: 'Weak Password Business'
  },

  invalidEmailUser: {
    email: 'not-an-email',
    password: 'ValidPass123!',
    businessName: 'Invalid Email Business'
  }
};

export const testTenants = {
  defaultTenant: {
    name: 'Test Survey Business',
    slug: 'test-survey-business',
    adminEmail: '<EMAIL>'
  }
};

export const validationMessages = {
  email: {
    required: 'Email is required',
    invalid: 'Please enter a valid email address'
  },
  password: {
    required: 'Password is required',
    tooShort: 'Password must be at least 8 characters',
    tooWeak: 'Password must contain uppercase, lowercase, numbers and special characters'
  },
  confirmPassword: {
    required: 'Please confirm your password',
    mismatch: 'Passwords must match'
  },
  businessName: {
    required: 'Business name is required'
  },
  terms: {
    required: 'You must accept the terms and conditions'
  }
};

export const successMessages = {
  signup: 'Account created successfully! Please check your email for verification.',
  emailSent: 'Verification email sent',
  passwordReset: 'Password reset email sent',
  login: 'Welcome back!'
};

export const errorMessages = {
  emailExists: 'An account with this email already exists',
  invalidCredentials: 'Invalid email or password',
  networkError: 'Network error. Please try again.',
  serverError: 'Server error. Please try again later.'
};

// Test data generators
export function generateUniqueEmail(): string {
  const timestamp = Date.now();
  return `test.user.${timestamp}@example.com`;
}

export function generateBusinessName(): string {
  const timestamp = Date.now();
  return `Test Business ${timestamp}`;
}

// Performance benchmarks
export const performanceTargets = {
  pageLoad: 2000, // 2 seconds
  formValidation: 100, // 100ms
  apiResponse: 500, // 500ms
  emailDelivery: 30000 // 30 seconds
};
