# Task 002-006: Implement Survey Types Configuration

**Parent Use Case:** 002-business-profile-setup  
**Task ID:** 002-006  
**Title:** Implement Survey Types Configuration  
**Estimated Development Time:** 1-2 hours (including tests/docs)  
**Estimated Agent Time:** 2-3 minutes  

## Description

Create a survey types configuration system that allows surveyors to define their primary service offerings. This system must align with UK compliance requirements, integrate with professional qualifications, and support the creation of appropriate inspection templates and workflows.

## Acceptance Criteria

- [ ] Pre-populated list of UK survey types (asbestos, legionella, fire safety, etc.)
- [ ] Multi-select functionality for multiple survey specializations
- [ ] Custom survey type entry for specialized services
- [ ] Integration with professional qualifications (qualification requirements)
- [ ] Survey type descriptions and compliance standards
- [ ] Service pricing tier indicators (basic, standard, premium)
- [ ] Survey type availability toggle (active/inactive services)
- [ ] Validation against professional qualifications held

## Deliverables Checklist

### Survey Types Data Structure
- [ ] Create survey types reference data
- [ ] Define UK compliance standards for each type
- [ ] Add required qualifications mapping
- [ ] Include survey type categories and subcategories
- [ ] Create survey complexity indicators

### Survey Types Selector Component
- [ ] Create SurveyTypesSelector React component
- [ ] Implement multi-select with search functionality
- [ ] Add survey type information tooltips
- [ ] Create custom survey type entry form
- [ ] Add service tier selection per survey type

### Qualification Integration
- [ ] Validate survey types against held qualifications
- [ ] Show qualification requirements for each survey type
- [ ] Warn when qualifications are missing or expired
- [ ] Suggest additional qualifications for expanded services
- [ ] Display qualification compliance status

### Service Configuration
- [ ] Add service availability toggles
- [ ] Implement service tier selection (basic/standard/premium)
- [ ] Create service description fields
- [ ] Add estimated duration and pricing guidance
- [ ] Include equipment requirements per survey type

### Validation Logic
- [ ] Ensure at least one survey type is selected
- [ ] Validate qualification requirements are met
- [ ] Check for conflicting survey type combinations
- [ ] Validate custom survey type entries
- [ ] Ensure compliance with UK regulations

### Testing
- [ ] Unit tests for survey type validation
- [ ] Tests for qualification integration
- [ ] Integration tests with qualifications component
- [ ] Tests for custom survey type handling
- [ ] Mobile interface testing

### Documentation
- [ ] UK survey type standards reference
- [ ] Qualification requirements mapping
- [ ] Component usage guide
- [ ] Compliance standards documentation

## Dependencies

- **Required Before Starting:**
  - 002-002 (Business Profile Form Component)
  - 002-005 (Professional Qualifications Management - for validation)
  - UK survey type research and compliance mapping

- **Blocks These Tasks:**
  - 002-007 (Report Branding Configuration - survey types affect branding)
  - 002-008 (Business Profile API Endpoints)

## Technical Considerations

### UK Survey Type Standards
- Asbestos surveys (Management, Refurbishment, Demolition)
- Legionella risk assessments
- Fire safety assessments
- Air quality monitoring
- Noise assessments
- Vibration monitoring
- Consider emerging survey types and regulations

### Qualification Mapping
- Map each survey type to required qualifications
- Handle qualification prerequisites and combinations
- Support for equivalent international qualifications
- Plan for qualification standard updates

### Business Logic
- Some survey types require multiple qualifications
- Certain combinations may have regulatory restrictions
- Consider insurance requirements per survey type
- Plan for regional regulation variations

### User Experience
- Clear indication of qualification requirements
- Intuitive grouping of related survey types
- Mobile-optimized selection interface
- Progressive disclosure of advanced options

### Future Integration
- Link to inspection template selection
- Integration with scheduling and pricing
- Connection to equipment management
- Support for service package creation

## Definition of Done

- [ ] Survey types selector component works correctly
- [ ] Pre-populated UK survey types are accurate
- [ ] Qualification validation functions properly
- [ ] Custom survey type entry works
- [ ] Integration with qualifications is seamless
- [ ] Service configuration options function
- [ ] All validation rules work correctly
- [ ] Mobile interface is user-friendly
- [ ] All tests pass
- [ ] Accessibility compliance verified
- [ ] Code review completed
- [ ] Documentation complete

## Notes

- Survey type selection directly impacts business capabilities
- Qualification requirements must be strictly enforced for compliance
- Consider that surveyors may specialize or offer broad services
- Survey types determine available inspection templates
- Plan for future integration with pricing and scheduling
- Some survey types may require specific equipment or methods
- Regulatory compliance is critical for professional credibility
