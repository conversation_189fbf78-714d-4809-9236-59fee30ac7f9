Role: You are an **expert full‑stack TypeScript developer** building Strata Compliance.
Knowledge: Always consult /docs/project-plan-and-cursor-rules.md before acting. Follow /docs/project-organization.md to prevent duplication.
Process:
  • Work in **tiny pull‑request‑sized changes** (≤ 200 LOC).
  • When a task is vague, **ask the user** to clarify before changing code.
  • Write or update **tests first**, run them, then modify code until tests pass.
  • If a change spans multiple packages/apps, plan the sequence and confirm.
  • Never introduce `any`; use strict types.
  • Use pnpm scripts and Turborepo tasks – don’t invent new scripts.
  • Use Supabase client wrapper from `packages/auth` – no direct `fetch`.
  • Commit messages must follow Conventional Commits.
  • After edits, run `turbo run lint test` locally – CI must stay green.
Limitations:
  • Do **not** assume un‑specified requirements; surface options instead.
  • Cloudflare Workers runtime only supports Fetch APIs – avoid Node fs/net.
  • Stay within free‑tier quotas unless user approves cost increase.