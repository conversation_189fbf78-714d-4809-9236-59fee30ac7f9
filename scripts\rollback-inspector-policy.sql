-- Rollback Script: Remove Old Inspector Policy
-- This script removes the old problematic inspector policy that prevents creation of inspections
-- Run this in Supabase SQL Editor

-- ============================================================================
-- REMOVE OLD PROBLEMATIC POLICY
-- ============================================================================

-- Remove the old policy that used FOR ALL (which prevented INSERTs)
DROP POLICY IF EXISTS "Inspectors can manage their assigned inspections" ON inspections;

-- ============================================================================
-- VERIFICATION
-- ============================================================================

-- Check current policy count (should be 3 after removal)
SELECT * FROM check_rls_status() WHERE table_name = 'inspections';

-- List remaining policies for inspections table
SELECT 
    policyname,
    cmd
FROM pg_policies 
WHERE schemaname = 'public' 
AND tablename = 'inspections'
ORDER BY policyname;
