# User Story: Client Management

- **ID:** 003-client-management
- **Title:** Create and manage client database from mobile app
- **As a:** solo surveyor
- **I want to:** add, edit, and organize my client information
- **So that:** I can efficiently manage my business relationships and contacts

## Acceptance Criteria

- [ ] User can create new client records with name, contact details, and address
- [ ] User can upload client logo/branding (optional)
- [ ] User can add multiple contact persons per client
- [ ] User can categorize clients (e.g., commercial, residential, local authority)
- [ ] User can search and filter client list
- [ ] User can edit existing client information
- [ ] User can archive/deactivate clients
- [ ] Client data is stored locally and synced when online
- [ ] User can view client history (sites, past inspections)
- [ ] User can add notes/special instructions per client

## Dependencies

- 001-authentication-signup
- 002-business-profile-setup
- Local database schema for clients

## Notes

- Client categories should align with UK market segments from user-functionality-analysis.md
- Must support offline creation and editing
- Foundation for site management and inspection workflows