# Task 003-001: Create Client Data Model and Schema

**Parent Use Case:** 003-client-management  
**Task ID:** 003-001  
**Title:** Create Client Data Model and Schema  
**Estimated Development Time:** 1-2 hours (including tests/docs)  
**Estimated Agent Time:** 2-3 minutes  

## Description

Define and implement the client data structure in Supabase and create corresponding TypeScript interfaces. This forms the foundation for all client management functionality, establishing the database schema, RLS policies, and type definitions that other components will use.

## Acceptance Criteria

- [ ] Client table exists in Supabase with all required fields
- [ ] Contact persons table exists with proper relationships
- [ ] RLS policies are implemented for multi-tenant data isolation
- [ ] TypeScript interfaces are defined and exported
- [ ] Database migration scripts are created and documented
- [ ] Table relationships are properly defined with foreign keys
- [ ] Data validation rules are implemented at the database level
- [ ] Unit tests validate data model constraints
- [ ] Documentation explains the data model structure and relationships

## Deliverables Checklist

### Database Schema
- [ ] Create `clients` table in Supabase
- [ ] Add required columns: id, tenant_id, name, trading_name, client_type, address fields, etc.
- [ ] Create `client_contacts` table for multiple contact persons
- [ ] Implement proper data types and constraints
- [ ] Add created_at, updated_at timestamps
- [ ] Create indexes for performance optimization

### Security & Access Control
- [ ] Implement RLS policies for tenant isolation on clients table
- [ ] Implement RLS policies for tenant isolation on client_contacts table
- [ ] Create policy for users to read their tenant's clients
- [ ] Create policy for users to manage their tenant's clients
- [ ] Test RLS policies with different user scenarios

### TypeScript Interfaces
- [ ] Define Client interface with all fields
- [ ] Define ClientContact interface for contact persons
- [ ] Define CreateClientRequest interface
- [ ] Define UpdateClientRequest interface
- [ ] Define ClientCategory enum for UK market segments
- [ ] Export interfaces from shared types package

### Migration & Documentation
- [ ] Create database migration script for clients table
- [ ] Create database migration script for client_contacts table
- [ ] Create rollback migration scripts
- [ ] Document table structure and field purposes
- [ ] Document RLS policy logic and security model

### Testing
- [ ] Write unit tests for TypeScript interface validation
- [ ] Test database constraints and validation rules
- [ ] Test RLS policies with multiple tenants
- [ ] Verify migration scripts work correctly
- [ ] Test foreign key relationships

## Dependencies

- **Required Before Starting:**
  - 001-authentication-signup (user authentication system must exist)
  - 002-business-profile-setup (business profile data model for tenant_id)

- **Blocks These Tasks:**
  - 003-002 (Core Client Form Component)
  - 003-009 (Client API Endpoints)
  - All other client management tasks

## Technical Considerations

- **Multi-tenant Architecture**: All client data must be isolated by tenant_id using RLS policies
- **Mobile Optimization**: Database queries should be optimized for mobile performance
- **UK-Specific Requirements**: Client categories should align with UK market segments (commercial, residential, local authority)
- **Offline Functionality**: Schema should support efficient local caching and sync
- **Data Relationships**: Client serves as parent for sites and inspections - foreign key constraints essential
- **Search Requirements**: Indexes needed for name, address, and category searches

## Definition of Done

- [ ] All database tables created with proper constraints
- [ ] RLS policies tested and verified secure
- [ ] TypeScript interfaces exported and documented
- [ ] Migration scripts tested in development environment
- [ ] Unit tests passing with 100% coverage
- [ ] Documentation reviewed and approved
- [ ] Ready for API endpoint development and form integration