# User Story: Basic Scheduling and Calendar

- **ID:** 011-basic-scheduling
- **Title:** Schedule inspections and manage calendar
- **As a:** solo surveyor managing multiple clients and sites
- **I want to:** schedule inspections and view my calendar
- **So that:** I can organize my work and meet client deadlines

## Acceptance Criteria

- [ ] User can view calendar with scheduled inspections
- [ ] User can create new scheduled inspections with date/time
- [ ] User can set inspection duration and travel time
- [ ] User can view daily, weekly, and monthly calendar views
- [ ] User can reschedule inspections by dragging on calendar
- [ ] User can set recurring inspections for regular clients
- [ ] User can add calendar reminders and notifications
- [ ] User can view inspection details from calendar entries
- [ ] User can mark inspections as completed or cancelled
- [ ] User can export calendar entries to external calendars (ICS)
- [ ] Calendar data is stored locally and synced when online

## Dependencies

- 005-inspection-creation
- 003-client-management
- 004-site-management
- Calendar component library

## Notes

- Basic scheduling for MVP, advanced features reserved for desktop (project plan)
- Export to Google/Outlook calendars important for solo workers
- Must work offline with sync when connected
- Foundation for route planning and advanced scheduling features