{"version": 3, "file": "supabaseClient.js", "sourceRoot": "", "sources": ["supabaseClient.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,uBAAuB,CAAC;AAErD,2CAA2C;AAC3C,SAAS,SAAS,CAAC,IAAY;IAC7B,0CAA0C;IAC1C,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE,CAAC;QAClC,yDAAyD;QACzD,IAAI,OAAO,MAAM,CAAC,IAAI,KAAK,WAAW,IAAK,MAAM,CAAC,IAAY,CAAC,GAAG,EAAE,CAAC;YACnE,OAAQ,MAAM,CAAC,IAAY,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;QAC9C,CAAC;QACD,uEAAuE;QACvE,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,0CAA0C;IAC1C,IAAI,OAAO,OAAO,KAAK,WAAW,IAAI,OAAO,CAAC,GAAG,EAAE,CAAC;QAClD,OAAO,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;IACjC,CAAC;IAED,OAAO,EAAE,CAAC;AACZ,CAAC;AAED,MAAM,WAAW,GACf,SAAS,CAAC,0BAA0B,CAAC,IAAI,SAAS,CAAC,mBAAmB,CAAC,IAAI,EAAE,CAAC;AAChF,MAAM,eAAe,GACnB,SAAS,CAAC,+BAA+B,CAAC;IAC1C,SAAS,CAAC,wBAAwB,CAAC;IACnC,EAAE,CAAC;AAEL,OAAO,CAAC,GAAG,CAAC,4BAA4B,EAAE;IACxC,GAAG,EAAE,WAAW,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,SAAS;IAC3C,GAAG,EAAE,eAAe,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,SAAS;IAC/C,SAAS,EAAE,WAAW,CAAC,MAAM;IAC7B,SAAS,EAAE,eAAe,CAAC,MAAM;CAClC,CAAC,CAAC;AAEH,MAAM,CAAC,MAAM,QAAQ,GAAG,YAAY,CAAC,WAAW,EAAE,eAAe,CAAC,CAAC"}