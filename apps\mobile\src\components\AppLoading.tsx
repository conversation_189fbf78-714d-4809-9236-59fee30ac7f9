import { IonContent, IonPage, IonSpinner } from '@ionic/react';
import React from 'react';

const AppLoading: React.FC = () => {
  return (
    <IonPage>
      <IonContent className="ion-padding">
        <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
          <div style={{ textAlign: 'center' }}>
            <IonSpinner name="crescent" />
            <p>Loading...</p>
          </div>
        </div>
      </IonContent>
    </IonPage>
  );
};

export default AppLoading;
