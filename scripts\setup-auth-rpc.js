#!/usr/bin/env node

/**
 * Authentication Setup using RPC calls
 * This script configures RLS policies using proper Supabase RPC methods
 */

import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://fwktrittbrmqarkipcpz.supabase.co';
const serviceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ3a3RyaXR0YnJtcWFya2lwY3B6Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MDE4OTE5NSwiZXhwIjoyMDY1NzY1MTk1fQ.ldI65o8yJCY76i2LRi8eZA_ljv2peuomIyA9sucAg0M';

const supabase = createClient(supabaseUrl, serviceKey);

async function createRLSSetupFunction() {
  console.log('🔧 Creating RLS Setup Function...');
  
  const setupFunctionSQL = `
    CREATE OR REPLACE FUNCTION setup_rls_policies()
    RETURNS text
    LANGUAGE plpgsql
    SECURITY DEFINER
    AS $$
    DECLARE
      result text := '';
    BEGIN
      -- Enable RLS on all tables
      ALTER TABLE tenants ENABLE ROW LEVEL SECURITY;
      ALTER TABLE tenant_users ENABLE ROW LEVEL SECURITY;
      ALTER TABLE clients ENABLE ROW LEVEL SECURITY;
      ALTER TABLE properties ENABLE ROW LEVEL SECURITY;
      ALTER TABLE sites ENABLE ROW LEVEL SECURITY;
      ALTER TABLE inspections ENABLE ROW LEVEL SECURITY;
      
      result := result || 'RLS enabled on all tables. ';
      
      -- Drop existing policies
      DROP POLICY IF EXISTS "Users can view their own tenant" ON tenants;
      DROP POLICY IF EXISTS "Only tenant admins can update tenant details" ON tenants;
      DROP POLICY IF EXISTS "Users can view their own tenant memberships" ON tenant_users;
      DROP POLICY IF EXISTS "Tenant admins can manage tenant users" ON tenant_users;
      DROP POLICY IF EXISTS "Users can view clients in their tenant" ON clients;
      DROP POLICY IF EXISTS "Schedulers and admins can manage clients" ON clients;
      
      -- Create tenant policies
      CREATE POLICY "Users can view their own tenant"
        ON tenants FOR SELECT
        USING (id = (auth.jwt() ->> 'tenant_id')::uuid);
        
      CREATE POLICY "Only tenant admins can update tenant details"
        ON tenants FOR UPDATE
        USING (
          id = (auth.jwt() ->> 'tenant_id')::uuid 
          AND (auth.jwt() ->> 'role') = 'TenantAdmin'
        );
      
      -- Create tenant_users policies
      CREATE POLICY "Users can view their own tenant memberships"
        ON tenant_users FOR SELECT
        USING (tenant_id = (auth.jwt() ->> 'tenant_id')::uuid);
        
      CREATE POLICY "Tenant admins can manage tenant users"
        ON tenant_users FOR ALL
        USING (
          tenant_id = (auth.jwt() ->> 'tenant_id')::uuid 
          AND (auth.jwt() ->> 'role') = 'TenantAdmin'
        );
      
      -- Create clients policies
      CREATE POLICY "Users can view clients in their tenant"
        ON clients FOR SELECT
        USING (tenant_id = (auth.jwt() ->> 'tenant_id')::uuid);
        
      CREATE POLICY "Schedulers and admins can manage clients"
        ON clients FOR ALL
        USING (
          tenant_id = (auth.jwt() ->> 'tenant_id')::uuid 
          AND (auth.jwt() ->> 'role') IN ('TenantAdmin', 'Scheduler')
        );
      
      -- Create properties policies
      CREATE POLICY "Users can view properties in their tenant"
        ON properties FOR SELECT
        USING (tenant_id = (auth.jwt() ->> 'tenant_id')::uuid);
        
      CREATE POLICY "Schedulers and admins can manage properties"
        ON properties FOR ALL
        USING (
          tenant_id = (auth.jwt() ->> 'tenant_id')::uuid 
          AND (auth.jwt() ->> 'role') IN ('TenantAdmin', 'Scheduler')
        );
      
      -- Create sites policies
      CREATE POLICY "Users can view sites in their tenant"
        ON sites FOR SELECT
        USING (tenant_id = (auth.jwt() ->> 'tenant_id')::uuid);
        
      CREATE POLICY "Schedulers and admins can manage sites"
        ON sites FOR ALL
        USING (
          tenant_id = (auth.jwt() ->> 'tenant_id')::uuid 
          AND (auth.jwt() ->> 'role') IN ('TenantAdmin', 'Scheduler')
        );
      
      -- Create inspections policies
      CREATE POLICY "Users can view inspections in their tenant"
        ON inspections FOR SELECT
        USING (tenant_id = (auth.jwt() ->> 'tenant_id')::uuid);
        
      CREATE POLICY "Schedulers and admins can manage all inspections"
        ON inspections FOR ALL
        USING (
          tenant_id = (auth.jwt() ->> 'tenant_id')::uuid 
          AND (auth.jwt() ->> 'role') IN ('TenantAdmin', 'Scheduler')
        );
      
      result := result || 'All RLS policies created successfully.';
      
      RETURN result;
    END;
    $$;
  `;
  
  try {
    const { data, error } = await supabase.rpc('exec', { sql: setupFunctionSQL });
    
    if (error) {
      console.log(`   ❌ Error creating setup function: ${error.message}`);
      return false;
    } else {
      console.log(`   ✅ RLS setup function created`);
      return true;
    }
  } catch (e) {
    console.log(`   ❌ Error: ${e.message}`);
    return false;
  }
}

async function executeRLSSetup() {
  console.log('\n🔒 Executing RLS Setup...');
  
  try {
    const { data, error } = await supabase.rpc('setup_rls_policies');
    
    if (error) {
      console.log(`   ❌ Error executing RLS setup: ${error.message}`);
      return false;
    } else {
      console.log(`   ✅ RLS setup completed: ${data}`);
      return true;
    }
  } catch (e) {
    console.log(`   ❌ Error: ${e.message}`);
    return false;
  }
}

async function createUserManagementFunctions() {
  console.log('\n👤 Creating User Management Functions...');
  
  const userFunctionsSQL = `
    -- Function to get user tenant info
    CREATE OR REPLACE FUNCTION get_user_tenant_info(user_id uuid)
    RETURNS TABLE(tenant_id uuid, role text, first_name text, last_name text)
    LANGUAGE plpgsql
    SECURITY DEFINER
    AS $$
    BEGIN
      RETURN QUERY
      SELECT tu.tenant_id, tu.role, tu.first_name, tu.last_name
      FROM tenant_users tu
      WHERE tu.user_id = get_user_tenant_info.user_id
      AND tu.status = 'active'
      LIMIT 1;
    END;
    $$;
    
    -- Function to add user to tenant
    CREATE OR REPLACE FUNCTION add_user_to_tenant(
      p_tenant_id uuid,
      p_user_id uuid,
      p_role text,
      p_first_name text DEFAULT NULL,
      p_last_name text DEFAULT NULL
    )
    RETURNS uuid
    LANGUAGE plpgsql
    SECURITY DEFINER
    AS $$
    DECLARE
      new_id uuid;
    BEGIN
      INSERT INTO tenant_users (tenant_id, user_id, role, first_name, last_name, status)
      VALUES (p_tenant_id, p_user_id, p_role, p_first_name, p_last_name, 'active')
      RETURNING id INTO new_id;
      
      RETURN new_id;
    END;
    $$;
    
    -- Function to check RLS status
    CREATE OR REPLACE FUNCTION check_rls_status()
    RETURNS TABLE(table_name text, rls_enabled boolean)
    LANGUAGE plpgsql
    SECURITY DEFINER
    AS $$
    BEGIN
      RETURN QUERY
      SELECT 
        t.tablename::text,
        t.rowsecurity
      FROM pg_tables t
      WHERE t.schemaname = 'public'
      AND t.tablename IN ('tenants', 'tenant_users', 'clients', 'properties', 'sites', 'inspections');
    END;
    $$;
  `;
  
  try {
    const { data, error } = await supabase.rpc('exec', { sql: userFunctionsSQL });
    
    if (error) {
      console.log(`   ❌ Error creating user functions: ${error.message}`);
      return false;
    } else {
      console.log(`   ✅ User management functions created`);
      return true;
    }
  } catch (e) {
    console.log(`   ❌ Error: ${e.message}`);
    return false;
  }
}

async function testRLSEnforcement() {
  console.log('\n🧪 Testing RLS Enforcement...');
  
  const anonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ3a3RyaXR0YnJtcWFya2lwY3B6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAxODkxOTUsImV4cCI6MjA2NTc2NTE5NX0.cbfaZXV4NwBwRXVSRF0xSzWVUTmfXlwZa4ptuOF0se0';
  const anonClient = createClient(supabaseUrl, anonKey);
  
  const tables = ['tenants', 'tenant_users', 'clients'];
  
  for (const table of tables) {
    try {
      const { data, error } = await anonClient.from(table).select('*').limit(1);
      
      if (error) {
        if (error.message.includes('RLS') || error.message.includes('policy') || error.message.includes('permission')) {
          console.log(`   ✅ ${table}: RLS working (${error.message})`);
        } else {
          console.log(`   ⚠️  ${table}: Unexpected error (${error.message})`);
        }
      } else {
        console.log(`   ❌ ${table}: RLS not enforced (returned ${data?.length || 0} rows)`);
      }
    } catch (e) {
      console.log(`   ✅ ${table}: RLS working (access denied)`);
    }
  }
}

async function verifySetup() {
  console.log('\n📊 Verifying Setup...');
  
  try {
    // Check RLS status using our custom function
    const { data: rlsData, error: rlsError } = await supabase.rpc('check_rls_status');
    
    if (rlsError) {
      console.log(`   ❌ Error checking RLS status: ${rlsError.message}`);
    } else {
      console.log('   📋 RLS Status:');
      rlsData?.forEach(table => {
        console.log(`      ${table.table_name}: ${table.rls_enabled ? '✅ Enabled' : '❌ Disabled'}`);
      });
    }
    
  } catch (error) {
    console.log(`   ❌ Error verifying setup: ${error.message}`);
  }
}

async function main() {
  console.log('🚀 Authentication Setup via RPC');
  console.log('=================================\n');
  
  // Create setup function
  const setupCreated = await createRLSSetupFunction();
  if (!setupCreated) {
    console.log('❌ Failed to create setup function. Exiting.');
    return;
  }
  
  // Execute RLS setup
  const setupExecuted = await executeRLSSetup();
  if (!setupExecuted) {
    console.log('❌ Failed to execute RLS setup. Exiting.');
    return;
  }
  
  // Create user management functions
  await createUserManagementFunctions();
  
  // Test RLS enforcement
  await testRLSEnforcement();
  
  // Verify setup
  await verifySetup();
  
  console.log('\n🎉 Authentication setup complete!');
  console.log('\n📋 Next Steps:');
  console.log('1. JWT claims still need manual configuration in Supabase Dashboard');
  console.log('2. Go to Authentication > Hooks');
  console.log('3. Test user signup and use add_user_to_tenant() function');
}

main().catch(console.error);
