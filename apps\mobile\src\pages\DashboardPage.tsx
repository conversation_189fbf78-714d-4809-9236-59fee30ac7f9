import {
  IonButton,
  IonContent,
  IonItem,
  IonLabel,
  IonList,
  IonPage,
  IonSpinner,
  IonText,
} from '@ionic/react';
import type { AuthError, Site, User } from '@strata/core';
import React, { useEffect, useState } from 'react';
import { useApi } from '../lib/api';

// Extended Site type with client data from API join
type SiteWithClient = Site & {
  client?: {
    id: string;
    name: string;
  };
};

interface DashboardPageProps {
  user: User | null;
  signOut: () => Promise<AuthError | null>;
}

const DashboardPage: React.FC<DashboardPageProps> = ({ user, signOut }) => {
  const [sites, setSites] = useState<SiteWithClient[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const api = useApi();

  // Load sites when component mounts
  useEffect(() => {
    const loadSites = async () => {
      try {
        setLoading(true);
        setError(null);

        const sitesData = await api.getSites();
        setSites(sitesData);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load sites');
      } finally {
        setLoading(false);
      }
    };

    loadSites();
  }, []);

  return (
    <IonPage>
      <IonContent className="ion-padding">
        <h2>Surveyor Dashboard</h2>
        <p>Welcome, {user?.email}</p>

        <h3>Your Sites</h3>

        {loading && <IonSpinner />}

        {error && (
          <IonText color="danger">
            <p>Error loading sites: {error}</p>
          </IonText>
        )}

        {!loading && !error && sites.length === 0 && (
          <IonText>
            <p>No sites found for your tenant.</p>
          </IonText>
        )}

        {!loading && !error && sites.length > 0 && (
          <IonList>
            {sites.map((site) => (
              <IonItem key={site.id}>
                <IonLabel>
                  <h3>{site.name}</h3>
                  {site.description && <p>{site.description}</p>}
                  {site.client && <p>Client: {site.client.name}</p>}
                  {site.address_line_1 && (
                    <p>{site.address_line_1}, {site.city}</p>
                  )}
                </IonLabel>
              </IonItem>
            ))}
          </IonList>
        )}

        <IonButton onClick={() => signOut()}>Sign Out</IonButton>
      </IonContent>
    </IonPage>
  );
};

export default DashboardPage;
