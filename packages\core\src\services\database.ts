// Database service for Strata Compliance
// Uses Supabase client wrapper from packages/auth

import type {
  Client,
  ClientInsert,
  ClientUpdate,
  Inspection,
  InspectionInsert,
  InspectionUpdate,
  Property,
  PropertyInsert,
  Site,
  SiteInsert,
  Tenant,
  TenantInsert,
  TenantUpdate,
  TenantUser,
} from "../types/index.js";

import type { SupabaseClient } from "@supabase/supabase-js";

export class DatabaseService {
  constructor(private supabase: SupabaseClient) {}

  // Authentication and user management
  async addUserToTenant(
    tenantId: string,
    userId: string,
    role: "TenantAdmin" | "Scheduler" | "Inspector" | "ClientUser",
    firstName?: string,
    lastName?: string
  ): Promise<TenantUser> {
    const { data, error } = await this.supabase
      .from("tenant_users")
      .insert({
        tenant_id: tenantId,
        user_id: userId,
        role,
        first_name: firstName,
        last_name: lastName,
        status: "active",
      })
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  async getUserTenantInfo(userId: string): Promise<TenantUser | null> {
    const { data, error } = await this.supabase
      .from("tenant_users")
      .select("*")
      .eq("user_id", userId)
      .eq("status", "active")
      .single();

    if (error) {
      if (error.code === "PGRST116") return null; // No rows returned
      throw error;
    }
    return data;
  }

  async getTenantUsers(tenantId: string): Promise<TenantUser[]> {
    const { data, error } = await this.supabase
      .from("tenant_users")
      .select("*")
      .eq("tenant_id", tenantId)
      .eq("status", "active")
      .order("created_at");

    if (error) throw error;
    return data || [];
  }

  // Tenant operations
  async getTenant(id: string): Promise<Tenant | null> {
    const { data, error } = await this.supabase
      .from("tenants")
      .select("*")
      .eq("id", id)
      .single();

    if (error) throw error;
    return data;
  }

  async createTenant(tenant: TenantInsert): Promise<Tenant> {
    const { data, error } = await this.supabase
      .from("tenants")
      .insert(tenant)
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  async updateTenant(id: string, updates: TenantUpdate): Promise<Tenant> {
    const { data, error } = await this.supabase
      .from("tenants")
      .update(updates)
      .eq("id", id)
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  // Client operations
  async getClients(tenantId: string): Promise<Client[]> {
    const { data, error } = await this.supabase
      .from("clients")
      .select("*")
      .eq("tenant_id", tenantId)
      .eq("status", "active")
      .order("name");

    if (error) throw error;
    return data || [];
  }

  async getClient(id: string): Promise<Client | null> {
    const { data, error } = await this.supabase
      .from("clients")
      .select("*")
      .eq("id", id)
      .single();

    if (error) throw error;
    return data;
  }

  async createClient(client: ClientInsert): Promise<Client> {
    const { data, error } = await this.supabase
      .from("clients")
      .insert(client)
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  async updateClient(id: string, updates: ClientUpdate): Promise<Client> {
    const { data, error } = await this.supabase
      .from("clients")
      .update(updates)
      .eq("id", id)
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  // Site operations
  async getSites(clientId: string): Promise<Site[]> {
    const { data, error } = await this.supabase
      .from("sites")
      .select("*")
      .eq("client_id", clientId)
      .eq("status", "active")
      .order("name");

    if (error) throw error;
    return data || [];
  }

  async getSitesForTenant(tenantId: string): Promise<Site[]> {
    const { data, error } = await this.supabase
      .from("sites")
      .select(
        `
        *,
        client:clients(id, name)
      `
      )
      .eq("tenant_id", tenantId)
      .eq("status", "active")
      .order("name");

    if (error) throw error;
    return data || [];
  }

  async createSite(site: SiteInsert): Promise<Site> {
    const { data, error } = await this.supabase
      .from("sites")
      .insert(site)
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  // Property operations
  async getProperties(siteId?: string, clientId?: string): Promise<Property[]> {
    let query = this.supabase
      .from("properties")
      .select("*")
      .eq("status", "active");

    if (siteId) {
      query = query.eq("site_id", siteId);
    } else if (clientId) {
      query = query.eq("client_id", clientId);
    }

    const { data, error } = await query.order("property_code");

    if (error) throw error;
    return data || [];
  }

  async getProperty(id: string): Promise<Property | null> {
    const { data, error } = await this.supabase
      .from("properties")
      .select("*")
      .eq("id", id)
      .single();

    if (error) throw error;
    return data;
  }

  async createProperty(property: PropertyInsert): Promise<Property> {
    const { data, error } = await this.supabase
      .from("properties")
      .insert(property)
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  // Inspection operations
  async getInspections(filters?: {
    propertyId?: string;
    inspectorId?: string;
    status?: string;
    dueAfter?: string;
    dueBefore?: string;
  }): Promise<Inspection[]> {
    let query = this.supabase.from("inspections").select(`
        *,
        property:properties(id, property_code, building_name, unit),
        assigned_inspector:tenant_users(id, first_name, last_name)
      `);

    if (filters?.propertyId) {
      query = query.eq("property_id", filters.propertyId);
    }
    if (filters?.inspectorId) {
      query = query.eq("assigned_inspector_id", filters.inspectorId);
    }
    if (filters?.status) {
      query = query.eq("status", filters.status);
    }
    if (filters?.dueAfter) {
      query = query.gte("due_date", filters.dueAfter);
    }
    if (filters?.dueBefore) {
      query = query.lte("due_date", filters.dueBefore);
    }

    const { data, error } = await query.order("due_date", { ascending: true });

    if (error) throw error;
    return data || [];
  }

  async getInspection(id: string): Promise<Inspection | null> {
    const { data, error } = await this.supabase
      .from("inspections")
      .select(
        `
        *,
        property:properties(*),
        assigned_inspector:tenant_users(id, first_name, last_name)
      `
      )
      .eq("id", id)
      .single();

    if (error) throw error;
    return data;
  }

  async createInspection(inspection: InspectionInsert): Promise<Inspection> {
    const { data, error } = await this.supabase
      .from("inspections")
      .insert(inspection)
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  async updateInspection(
    id: string,
    updates: InspectionUpdate
  ): Promise<Inspection> {
    const { data, error } = await this.supabase
      .from("inspections")
      .update(updates)
      .eq("id", id)
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  // User operations

  async getTenantUser(
    userId: string,
    tenantId: string
  ): Promise<TenantUser | null> {
    const { data, error } = await this.supabase
      .from("tenant_users")
      .select("*")
      .eq("user_id", userId)
      .eq("tenant_id", tenantId)
      .single();

    if (error) throw error;
    return data;
  }
}
