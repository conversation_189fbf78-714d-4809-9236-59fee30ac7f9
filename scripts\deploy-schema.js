#!/usr/bin/env node

import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';

const supabaseUrl = 'https://fwktrittbrmqarkipcpz.supabase.co';
const serviceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ3a3RyaXR0YnJtcWFya2lwY3B6Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MDE4OTE5NSwiZXhwIjoyMDY1NzY1MTk1fQ.ldI65o8yJCY76i2LRi8eZA_ljv2peuomIyA9sucAg0M';

const supabase = createClient(supabaseUrl, serviceKey);

async function runMigration(filePath, migrationName) {
  console.log(`🔄 Running migration: ${migrationName}`);
  
  try {
    const sql = fs.readFileSync(filePath, 'utf8');
    
    // Split SQL into individual statements (basic approach)
    const statements = sql
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));
    
    for (const statement of statements) {
      if (statement.trim()) {
        const { error } = await supabase.rpc('exec_sql', { sql_query: statement + ';' });
        
        if (error) {
          // Try direct execution if RPC fails
          const { error: directError } = await supabase.from('_temp_exec').select('*').limit(0);
          
          if (directError && directError.message.includes('does not exist')) {
            console.log(`   ⚠️  Cannot execute via RPC, statement: ${statement.substring(0, 50)}...`);
            console.log(`   💡 Please run this migration manually in Supabase SQL Editor`);
            return false;
          }
        }
      }
    }
    
    console.log(`   ✅ Migration completed: ${migrationName}`);
    return true;
    
  } catch (error) {
    console.log(`   ❌ Migration failed: ${migrationName}`);
    console.log(`   Error: ${error.message}`);
    return false;
  }
}

async function checkTableExists(tableName) {
  try {
    const { data, error } = await supabase.from(tableName).select('*').limit(1);
    return !error;
  } catch {
    return false;
  }
}

async function deploySchema() {
  console.log('🚀 Deploying Strata Compliance Database Schema');
  console.log('===============================================\n');
  
  // Check current state
  console.log('📋 Checking current database state...');
  const tables = ['tenants', 'tenant_users', 'clients', 'properties', 'sites', 'inspections'];
  const existingTables = [];
  
  for (const table of tables) {
    const exists = await checkTableExists(table);
    if (exists) {
      existingTables.push(table);
      console.log(`   ✅ Table '${table}' exists`);
    } else {
      console.log(`   ❌ Table '${table}' missing`);
    }
  }
  
  console.log(`\n📊 Status: ${existingTables.length}/${tables.length} tables exist\n`);
  
  // Run migrations
  const migrations = [
    '001_create_tenants.sql',
    '002_create_tenant_users.sql',
    '003_create_clients.sql',
    '004_create_properties.sql',
    '005_create_sites_and_inspections.sql'
  ];
  
  console.log('🔄 Running migrations...');
  
  for (const migration of migrations) {
    const migrationPath = path.join(process.cwd(), 'database', 'migrations', migration);
    
    if (fs.existsSync(migrationPath)) {
      const success = await runMigration(migrationPath, migration);
      if (!success) {
        console.log('\n❌ Migration failed. Manual intervention required.');
        console.log('📋 Next steps:');
        console.log('1. Go to https://fwktrittbrmqarkipcpz.supabase.co');
        console.log('2. Navigate to SQL Editor');
        console.log(`3. Run the contents of database/migrations/${migration}`);
        return false;
      }
    } else {
      console.log(`⚠️  Migration file not found: ${migration}`);
    }
  }
  
  // Verify final state
  console.log('\n🔍 Verifying deployment...');
  let allTablesExist = true;
  
  for (const table of tables) {
    const exists = await checkTableExists(table);
    if (exists) {
      console.log(`   ✅ Table '${table}' verified`);
    } else {
      console.log(`   ❌ Table '${table}' still missing`);
      allTablesExist = false;
    }
  }
  
  if (allTablesExist) {
    console.log('\n🎉 Database schema deployment completed successfully!');
    console.log('\n📋 Next steps:');
    console.log('1. Configure authentication JWT claims');
    console.log('2. Test RLS policies');
    console.log('3. Create sample tenant data');
  } else {
    console.log('\n⚠️  Some tables are still missing. Manual migration required.');
  }
  
  return allTablesExist;
}

deploySchema().catch(console.error);
