<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Strata Mobile App</title>
  <link rel="stylesheet" href="https://unpkg.com/@ionic/react/css/core.css" />
  <link rel="stylesheet" href="https://unpkg.com/@ionic/react/css/normalize.css" />
  <link rel="stylesheet" href="https://unpkg.com/@ionic/react/css/structure.css" />
  <link rel="stylesheet" href="https://unpkg.com/@ionic/react/css/typography.css" />
  <link rel="stylesheet" href="https://unpkg.com/@ionic/react/css/padding.css" />
  <link rel="stylesheet" href="https://unpkg.com/@ionic/react/css/float-elements.css" />
  <link rel="stylesheet" href="https://unpkg.com/@ionic/react/css/text-alignment.css" />
  <link rel="stylesheet" href="https://unpkg.com/@ionic/react/css/text-transformation.css" />
  <link rel="stylesheet" href="https://unpkg.com/@ionic/react/css/flex-utils.css" />
  <link rel="stylesheet" href="https://unpkg.com/@ionic/react/css/display.css" />
  <style>
    html,
    body {
      height: 100%;
      margin: 0;
      padding: 0;
    }

    #root {
      height: 100%;
      width: 100%;
    }
  </style>
</head>

<body>
  <div id="root"></div>
  <script type="module" src="/src/main.tsx"></script>
</body>

</html>