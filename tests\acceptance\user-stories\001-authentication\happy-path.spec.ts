import { expect, test } from "@playwright/test";
import {
  generateUniqueEmail,
  performanceTargets,
  testUsers,
} from "../../fixtures/test-data";
import { AuthPage } from "../../page-objects/auth-page";
import { DashboardPage } from "../../page-objects/dashboard-page";

test.describe("User Story 001: Authentication Happy Path", () => {
  let authPage: AuthPage;
  let dashboardPage: DashboardPage;
  let uniqueEmail: string;

  test.beforeEach(async ({ page }) => {
    authPage = new AuthPage(page);
    dashboardPage = new DashboardPage(page);
    uniqueEmail = generateUniqueEmail();

    // Navigate to auth page
    await authPage.goto();
  });

  test("Scenario 1: Page Load and Basic UI", async ({ page }) => {
    // Step 1: Measure page load performance
    const pageLoadTime = await authPage.measurePageLoad();
    expect(pageLoadTime).toBeLessThan(performanceTargets.pageLoad);
    console.log(`📊 Page load time: ${pageLoadTime}ms`);

    // Step 2: Check if we're redirected to dashboard (already logged in)
    if (page.url().includes("/dashboard")) {
      console.log(`✅ Already logged in - redirected to dashboard`);
      await dashboardPage.expectUserLoggedIn();
    } else {
      // Step 3: Test basic auth page elements
      await expect(
        page.locator('h1:has-text("Strata Compliance")')
      ).toBeVisible();
      console.log(`✅ Auth page loaded successfully`);
    }
  });

  test("Scenario 2: Password Authentication Flow", async ({ page }) => {
    // Test password-based sign up and sign in
    const testEmail = uniqueEmail;
    const testPassword = testUsers.validUser.password;

    // Step 1: Sign up with password
    await authPage.signUpWithPassword(testEmail, testPassword);

    // Step 2: Check for success or error handling
    // Note: This might show an error if email verification is required
    // or redirect to dashboard if auto-login is enabled

    const isStillOnAuth = await authPage.isOnAuthPage();
    if (isStillOnAuth) {
      // If still on auth page, try to sign in
      await authPage.signInWithPassword(testEmail, testPassword);

      // Check if we get redirected to dashboard
      try {
        await authPage.waitForRedirect();
        await dashboardPage.expectUserLoggedIn();
        console.log(`✅ Successfully logged in with password`);
      } catch (error) {
        // Might need email verification first
        console.log(`ℹ️ Email verification may be required`);
      }
    } else {
      // Already redirected, check dashboard
      await dashboardPage.expectUserLoggedIn();
      console.log(`✅ Auto-login after signup successful`);
    }
  });

  test("Scenario 3: UI Elements and Navigation", async ({ page }) => {
    // Test that all UI elements are present and functional

    // Step 1: Verify page loads and shows auth form
    await expect(
      page.locator('h1:has-text("Strata Compliance")')
    ).toBeVisible();

    // Step 2: Test segment switching (Magic Link vs Password)
    await authPage.switchToPassword();
    await expect(authPage.passwordSegment).toHaveClass(
      /segment-button-checked/
    );

    await authPage.switchToMagicLink();
    await expect(authPage.magicLinkSegment).toHaveClass(
      /segment-button-checked/
    );

    // Step 3: Test sign up/sign in toggle
    await authPage.switchToSignUp();
    await expect(page.locator("text=Create your account")).toBeVisible();

    await authPage.switchToSignIn();
    await expect(page.locator("text=Please sign in to continue")).toBeVisible();

    // Step 4: Test form inputs are functional
    await authPage.fillEmail("<EMAIL>");
    await expect(authPage.emailInput).toHaveValue("<EMAIL>");

    console.log("✅ All UI elements are working correctly");
  });
});
