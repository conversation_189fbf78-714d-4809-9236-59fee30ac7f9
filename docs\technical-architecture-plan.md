# Technical Architecture Plan - Strata Compliance

> **Purpose**: Comprehensive technical implementation strategy supporting per-seat licensing and enterprise scalability
> **Status**: Architecture Framework Complete
> **Last Updated**: 2025-01-26

## Executive Summary

Technical architecture designed to support per-seat licensing model with seamless scaling from solo workers to large enterprises, **building upon existing project decisions** from `docs/project-plan-and-cursor-rules.md` and incorporating lessons from Spotlite Compliance interface analysis.

## ⚠️ Alignment with Existing Project Decisions

This document **extends and aligns with** the established technical choices in the project plan:

**Confirmed Technology Stack:**

- **Mobile**: Ionic React (not React Native/Flutter)
- **Desktop**: React 19 + MUI v6 + FullCalendar Premium
- **API**: Cloudflare Pages Functions/Workers (not Node.js)
- **Database**: Supabase with RLS (not custom multi-tenant schema)
- **Build**: Turborepo + pnpm with remote caching
- **Hosting**: Cloudflare Pages (not Vercel/Netlify)

**Confirmed Architecture Patterns:**

- **Role System**: `TenantAdmin`, `Scheduler`, `Inspector`, `ClientUser`, `SoloWorker`
- **Data Hierarchy**: Multi-tenant → Clients → Sites → Assets
- **Offline Requirements**: 24h offline operation with IndexedDB cache
- **Security**: Supabase RLS + JWT forwarding from Workers

## 1. System Architecture Overview

### 1.1 Multi-Tenant SaaS Architecture

**Core Design Principles:**

- **Tenant isolation** for data security and compliance
- **Horizontal scalability** for enterprise growth
- **Mobile-first API design** with offline-first capabilities
- **Real-time synchronization** between mobile and webtop
- **Per-seat billing integration** with usage tracking

### 1.2 Technology Stack (Aligned with Existing Decisions)

**Frontend:**

- **Mobile**: Ionic React with Capacitor for cross-platform deployment
- **Desktop**: React 19 + MUI v6 + FullCalendar Premium for data-dense interfaces
- **Shared Components**: Design system in `packages/ui` ensuring UI consistency

**Backend:**

- **API**: Cloudflare Pages Functions (Workers runtime) with TypeScript
- **Database**: Supabase PostgreSQL with Row-Level Security (RLS) for multi-tenancy
- **Authentication**: Supabase Auth with JWT forwarding to Workers
- **File Storage**: Supabase Storage for documents and media
- **Real-time**: Supabase real-time subscriptions

**Infrastructure:**

- **Hosting**: Cloudflare Pages for static assets, Workers for API
- **CDN**: Cloudflare global edge network for performance
- **Build**: Turborepo + pnpm with remote caching
- **Monitoring**: Cloudflare Analytics + Supabase monitoring

## 2. Data Architecture & Multi-Tenancy

### 2.1 Tenant Isolation Strategy (Aligned with Existing RLS Design)

**Supabase RLS Schema Design:**

```sql
-- Core tenant structure (using established hierarchy)
tenants (id, name, plan_tier, created_at, settings)
users (id, tenant_id, email, role, seat_type, last_active)
clients (id, tenant_id, name, contact_info, created_at)
sites (id, client_id, name, address, hierarchy_data)
assets (id, site_id, name, type, metadata) -- Existing: Multi-tenant → Clients → Sites → Assets
inspections (id, asset_id, inspector_id, data, status)
documents (id, inspection_id, type, file_path, metadata)
role_permissions (id, role, permission, resource_type) -- Existing role system
audit_log (id, tenant_id, user_id, action, table_name, record_id, changes) -- Existing audit trail
```

**Supabase Row-Level Security (RLS):**

- **Automatic tenant filtering** via RLS policies on all tables
- **Established role system**: `TenantAdmin`, `Scheduler`, `Inspector`, `ClientUser`, `SoloWorker`
- **Permission-based data creation** configurable per role
- **JWT forwarding** from Cloudflare Workers for RLS enforcement

### 2.2 Hierarchical Data Management (Existing Pattern)

**Multi-tenant → Clients → Sites → Assets Structure:**

- **Established hierarchy** from existing project plan
- **Asset-based organization** supporting various inspection types
- **Inherited permissions** cascading down hierarchy levels via RLS
- **Audit trails** via existing trigger system into `audit_log` table
- **7-year retention** for historical reports and documents (configurable)

### 2.3 Document Management System

**File Organization:**

```
/tenants/{tenant_id}/
  /clients/{client_id}/
    /sites/{site_id}/
      /buildings/{building_id}/
        /inspections/{inspection_id}/
          /photos/
          /reports/
          /certificates/
        /floor_plans/
        /documents/
```

**Metadata Management:**

- **Document categorization** (Report, Certificate, Floor Plan, Photo)
- **Version control** with automatic backup retention
- **Access permissions** by user role and client relationship
- **Search indexing** for rapid document discovery

## 3. Mobile-First Architecture (Ionic React + Capacitor)

### 3.1 Offline-First Design (Existing Requirements)

**Local Data Storage (24h offline requirement):**

- **IndexedDB cache** for inspection data and metadata (existing requirement)
- **Capacitor filesystem** for photos, videos, and documents
- **Background sync** when connectivity restored (existing requirement)
- **Conflict resolution** for simultaneous edits

**Sync Strategy:**

```javascript
// Sync priority order
1. Critical inspection data (findings, compliance status)
2. Media files (photos, videos, voice notes)
3. Document metadata and references
4. Historical data and reports
```

### 3.2 Progressive Web App (PWA) Capabilities

**Cross-Platform Consistency:**

- **Shared codebase** between mobile app and webtop
- **Offline functionality** for webtop users in field scenarios
- **Push notifications** for assignment updates and deadlines
- **App store distribution** with web fallback

### 3.3 Real-Time Synchronization

**WebSocket Implementation:**

- **Live updates** for team coordination
- **Status broadcasting** for inspection progress
- **Conflict resolution** for simultaneous edits
- **Connection resilience** with automatic reconnection

## 4. Per-Seat Licensing Implementation

### 4.1 User Management & Billing

**Seat Tracking:**

```sql
-- Seat usage tracking
user_sessions (id, user_id, tenant_id, start_time, end_time, device_type)
billing_periods (id, tenant_id, start_date, end_date, seat_count, amount)
seat_allocations (id, tenant_id, user_id, seat_type, allocated_date, status)
```

**Billing Integration:**

- **Stripe/Paddle** for subscription management
- **Usage-based billing** with monthly seat counting
- **Automatic scaling** for seat additions/removals
- **Proration handling** for mid-cycle changes

### 4.2 Access Control & Enforcement (Aligned with Existing Tiers)

**Subscription Tiers (from existing project plan):**

- **Basic (Mobile-only)**: Solo worker, create inspections, manage clients/sites, basic reports
- **Professional**: Enhanced mobile features, basic client portal access
- **Enterprise**: Full desktop access, advanced scheduling, team management, enhanced reporting
- **Team**: Multi-user support with role-based permissions and invitation system

**Role-Based Enforcement (existing system):**

- **Login restrictions** based on seat availability and role permissions
- **Feature gating** by subscription tier (Basic/Professional/Enterprise/Team)
- **RLS enforcement** via established role system
- **Invitation-based onboarding** for team environments

### 4.3 Enterprise Features Architecture

**Multi-Client Management:**

- **Client isolation** within tenant boundaries
- **Bulk operations** for efficiency at scale
- **Reporting aggregation** across multiple clients
- **Permission inheritance** through client hierarchies

## 5. Integration Architecture

### 5.1 API-First Design

**RESTful API Structure:**

```
/api/v1/
  /auth/          # Authentication and authorization
  /tenants/       # Tenant management
  /users/         # User and seat management
  /clients/       # Client management
  /sites/         # Site and building hierarchy
  /inspections/   # Inspection data and workflows
  /documents/     # Document management
  /reports/       # Report generation
  /billing/       # Subscription and usage tracking
```

**GraphQL Layer:**

- **Efficient data fetching** for complex queries
- **Real-time subscriptions** for live updates
- **Type safety** with generated TypeScript definitions
- **Caching optimization** for performance

### 5.2 Third-Party Integrations

**Priority Integrations:**

- **Accounting Software**: Xero, Sage, QuickBooks APIs
- **Calendar Systems**: Google Calendar, Outlook integration
- **Laboratory Systems**: UKAS-accredited lab result APIs
- **Document Storage**: SharePoint, Dropbox, Google Drive
- **Communication**: Email, SMS, WhatsApp notifications

**Integration Framework:**

- **Webhook system** for real-time data exchange
- **OAuth 2.0** for secure third-party authentication
- **Rate limiting** and error handling
- **Integration marketplace** for custom connectors

### 5.3 White-Label Architecture

**Customization Capabilities:**

- **Branding customization** (logos, colors, domains)
- **Feature configuration** per tenant
- **Custom domain support** for enterprise clients
- **API white-labeling** for partner integrations

## 6. Performance & Scalability

### 6.1 Database Optimization

**Query Performance:**

- **Indexed tenant_id** on all multi-tenant tables
- **Materialized views** for complex reporting queries
- **Connection pooling** for efficient resource usage
- **Read replicas** for reporting and analytics

**Scaling Strategy:**

- **Horizontal partitioning** by tenant for large datasets
- **Caching layers** (Redis) for frequently accessed data
- **CDN integration** for global performance
- **Database sharding** for extreme scale scenarios

### 6.2 File Storage & Media Handling

**Supabase Storage Optimization:**

- **Automatic image compression** for mobile uploads
- **Progressive loading** for large document libraries
- **Thumbnail generation** for quick previews
- **Global CDN distribution** for fast access

**Storage Architecture:**

```
Storage Buckets:
- tenant-documents: Secure document storage
- tenant-media: Photos, videos, voice notes
- public-assets: Shared templates and resources
- temp-uploads: Temporary file processing
```

### 6.3 Monitoring & Observability

**Performance Tracking:**

- **Application Performance Monitoring** (APM)
- **Real User Monitoring** (RUM) for mobile experience
- **Database query analysis** and optimization
- **Error tracking** and alerting systems

**Business Metrics:**

- **Seat utilization** tracking for billing accuracy
- **Feature usage** analytics for product development
- **Customer health** scoring for retention
- **Performance benchmarks** for SLA compliance

## 7. Security & Compliance

### 7.1 Data Protection

**Encryption:**

- **End-to-end encryption** for sensitive inspection data
- **At-rest encryption** for database and file storage
- **In-transit encryption** (TLS 1.3) for all communications
- **Key management** with automatic rotation

**Access Control:**

- **Multi-factor authentication** (MFA) for all users
- **Role-based access control** (RBAC) with granular permissions
- **Session management** with automatic timeout
- **Audit logging** for all data access and modifications

### 7.2 Compliance Requirements

**UK Regulatory Compliance:**

- **GDPR compliance** with data portability and deletion
- **HSE data retention** requirements (7+ years)
- **UKAS traceability** for laboratory integrations
- **Building Safety Act** compliance features

**Industry Standards:**

- **ISO 27001** security management framework
- **SOC 2 Type II** compliance for enterprise clients
- **Penetration testing** and vulnerability assessments
- **Data backup** and disaster recovery procedures

## 8. Development & Deployment

### 8.1 Development Workflow

**CI/CD Pipeline:**

- **Automated testing** (unit, integration, e2e)
- **Code quality** checks and security scanning
- **Staging environments** for feature testing
- **Blue-green deployments** for zero-downtime updates

**Quality Assurance:**

- **Test-driven development** (TDD) practices
- **Mobile device testing** across platforms
- **Performance testing** under load
- **Security testing** and code reviews

### 8.2 Deployment Strategy

**Environment Management:**

- **Development**: Feature development and testing
- **Staging**: Pre-production validation
- **Production**: Live customer environment
- **Disaster Recovery**: Backup environment for continuity

**Scaling Automation:**

- **Auto-scaling** based on usage patterns
- **Load balancing** for high availability
- **Database scaling** with read replicas
- **CDN optimization** for global performance

---

**Next Steps**: Begin MVP development with core multi-tenant architecture, implement basic per-seat billing, and establish development workflow for rapid iteration and scaling.
