import { Locator, <PERSON>, expect } from "@playwright/test";

export class AuthPage {
  readonly page: Page;

  // Auth mode segments (Magic Link vs Password)
  readonly magicLinkSegment: Locator;
  readonly passwordSegment: Locator;

  // Form elements (shared between sign in/up)
  readonly emailInput: Locator;
  readonly passwordInput: Locator;

  // Sign in/up toggle buttons
  readonly signUpToggle: Locator;
  readonly signInToggle: Locator;

  // Action buttons
  readonly continueWithEmailButton: Locator;
  readonly signInButton: Locator;
  readonly createAccountButton: Locator;

  // Messages and feedback
  readonly successMessage: Locator;
  readonly errorMessage: Locator;
  readonly magicLinkSentMessage: Locator;

  // Loading states
  readonly loadingSpinner: Locator;

  constructor(page: Page) {
    this.page = page;

    // Auth mode segments - based on IonSegment structure
    this.magicLinkSegment = page.locator('ion-segment-button[value="magic"]');
    this.passwordSegment = page.locator('ion-segment-button[value="password"]');

    // Form elements - based on IonInput structure (shared between sign in/up)
    this.emailInput = page.locator('ion-input[label="Email"] input');
    this.passwordInput = page.locator('ion-input[label="Password"] input');

    // Sign in/up toggle buttons - based on IonButton structure
    this.signUpToggle = page.locator('ion-button:has-text("Sign Up")');
    this.signInToggle = page.locator('ion-button:has-text("Sign In")');

    // Action buttons
    this.continueWithEmailButton = page.locator(
      'ion-button:has-text("Continue with Email")'
    );
    this.signInButton = page.locator(
      'ion-button[expand="block"]:has-text("Sign In")'
    );
    this.createAccountButton = page.locator(
      'ion-button:has-text("Create Account")'
    );

    // Messages - based on IonText structure
    this.successMessage = page.locator('ion-text[color="success"]');
    this.errorMessage = page.locator('ion-text[color="danger"]');
    this.magicLinkSentMessage = page.locator(
      "text=Check your email! We've sent you a sign-in link."
    );

    // Loading states
    this.loadingSpinner = page.locator("ion-spinner");
  }

  // Navigation methods
  async goto() {
    await this.page.goto("/auth");
  }

  async switchToSignUp() {
    await this.signUpToggle.click();
  }

  async switchToSignIn() {
    await this.signInToggle.click();
  }

  async switchToMagicLink() {
    await this.magicLinkSegment.click();
  }

  async switchToPassword() {
    await this.passwordSegment.click();
  }

  // Form interaction methods
  async fillEmail(email: string) {
    await this.emailInput.fill(email);
  }

  async fillPassword(password: string) {
    await this.passwordInput.fill(password);
  }

  async signUpWithPassword(email: string, password: string) {
    await this.switchToSignUp();
    await this.fillEmail(email);
    await this.fillPassword(password);
    await this.createAccountButton.click();
  }

  async signInWithPassword(email: string, password: string) {
    await this.switchToSignIn();
    await this.switchToPassword();
    await this.fillEmail(email);
    await this.fillPassword(password);
    await this.signInButton.click();
  }

  async signInWithMagicLink(email: string) {
    await this.switchToSignIn();
    await this.switchToMagicLink();
    await this.fillEmail(email);
    await this.continueWithEmailButton.click();
  }

  // Validation methods
  async expectMagicLinkSent() {
    await expect(this.magicLinkSentMessage).toBeVisible();
  }

  async expectErrorMessage(message?: string) {
    await expect(this.errorMessage).toBeVisible();
    if (message) {
      await expect(this.errorMessage).toContainText(message);
    }
  }

  // Performance methods
  async measurePageLoad(): Promise<number> {
    const startTime = Date.now();
    await this.page.waitForLoadState("networkidle");
    return Date.now() - startTime;
  }

  // Wait for navigation after successful auth
  async waitForRedirect() {
    await this.page.waitForURL(/\/(dashboard)/, { timeout: 10000 });
  }

  // Check if we're on the auth page (not redirected)
  async isOnAuthPage(): Promise<boolean> {
    return this.page.url().includes("/auth");
  }
}
