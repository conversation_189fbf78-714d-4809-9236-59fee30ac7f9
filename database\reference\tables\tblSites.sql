﻿CREATE TABLE [dbo].[tblSites] (
    [Id]        UNIQUEIDENTIFIER CONSTRAINT [DF_Sites_Id] DEFAULT (newid()) NOT NULL,
    [Created]   DATETIME         CONSTRAINT [DF_Sites_Created] DEFAULT (getdate()) NOT NULL,
    [ClientId]  UNIQUEIDENTIFIER NOT NULL,
    [AddressId] UNIQUEIDENTIFIER NULL,
    [SiteName]  NVARCHAR (MAX)   NULL,
    [Archived] DATETIME NULL,
    [Deleted] DATETIME NULL,
    [ArchiveReason] NVARCHAR(MAX) NULL, 
    CONSTRAINT [PK_Sites] PRIMARY KEY CLUSTERED ([Id] ASC),
    CONSTRAINT [FK_Sites_Addresses] FOREIGN KEY ([AddressId]) REFERENCES [dbo].[tblAddresses] ([Id]),
    CONSTRAINT [FK_Sites_Clients] FOREIGN KEY ([ClientId]) REFERENCES [dbo].[tblClients] ([Id])
);

