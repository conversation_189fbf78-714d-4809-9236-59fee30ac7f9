# PocketSurvey – In-Depth Competitor Analysis

## 1. Product Overview

**PocketSurvey** is a mobile-oriented building surveying and inspection software platform designed to streamline compliance, property, and asset management projects. It enables fast mobile data collection on tablets or phones and produces professional reports instantly, aiming to save surveyors significant time and effort.

- **Platforms:** Mobile (iOS/Android), Desktop (PC)
- **Target Users:** Building surveyors, compliance inspectors, property managers, asset managers, housing authorities, consultancies, and teams
- **Core Promise:** "Inspect buildings on-site entering data just once, and then produce your building assessment reports in seconds on the same device."

---

## 2. Features & Capabilities

### 2.1 Survey Creation & Data Capture

- **Ready-to-Go Templates:** Wide range of pre-built survey templates for various compliance and property inspection types (asbestos, fire door, fire risk, legionella, HHSRS, damp & mould, planned maintenance, building condition, home purchase, etc.)
- **Custom Survey Designer:** Highly customizable survey templates, including configurable lists of building elements, descriptions, features, defects, and recommendations.
- **Mobile Data Entry:** Fast, on-site data capture using mobile devices; supports photos, drop-downs, and custom fields.
- **Photo Capture:** Integrated photo capture with annotation and gallery management.
- **Status Tracking:** Color-coded status indicators for items and jobs (scheduled, started, pending, completed).
- **Location Services:** Building maps dashboard with geolocation and address search.
- **Offline Support:** Full offline data collection; syncs when online.

### 2.2 Report Generation

- **Instant Reports:** Generate professional, branded PDF reports on-site or from desktop, including table of contents, executive summaries, detailed info, photos, and boilerplate sections.
- **Export Options:** Export reports as PDFs or spreadsheets.
- **Custom Branding:** Add company logo, contact details, and custom report titles.

### 2.3 Job & Workflow Management

- **Jobs Calendar:** Built-in, color-coded calendar for planning and viewing inspections by month, week, or day; supports team scheduling.
- **Job Progress Tracking:** Visual progress screens by status and date, with backlog and completion indicators.
- **Team Collaboration:** Multi-user/team support with user roles and permissions.
- **Copy/Duplicate Items:** Duplicate items and apply them to different buildings.

### 2.4 Client & Site Management

- **Buildings List:** Organized by town, with thumbnails and quick access to reports.
- **Building Details:** Detailed view with photos, inspection details, and action icons for adding items or generating reports.
- **Items List:** All assessed items with location, description, photo, and status.
- **Client Management:** Manage clients, users, and properties.

### 2.5 User Experience

- **Mobile-Friendly UX:** Designed for fast, efficient use in the field; large touch targets, quick navigation, minimal typing.
- **Customizable UI:** Users can turn features on/off to suit workflow (e.g., jobs, sites, priority assessments).
- **Help & Support:** In-app help, FAQs, and direct support access.

### 2.6 Additional Features

- **App Design:** Configure your own variant of the app, including custom drop-downs and workflow options.
- **Template Flexibility:** Easily create bespoke surveys for any inspection type.
- **Multi-Platform:** Use on mobile or desktop; reports can be generated from either.
- **Low-Cost Trial:** Entry pricing and trial options for new users.

---

## 3. Workflow Analysis

### 3.1 Typical User Journey

#### Solo Surveyor

1. **Select Template:** Choose a ready-to-go or custom survey template.
2. **Create Job:** Enter building and client details, schedule inspection.
3. **On-Site Survey:** Capture data, photos, and notes using mobile device.
4. **Generate Report:** Instantly create and review PDF report on-site or from desktop.
5. **Deliver Report:** Email or export report to client.
6. **Follow-Up:** Manage job progress and client communication.

#### Team/Consultancy

1. **Assign Jobs:** Managers assign jobs to team members via calendar.
2. **Field Execution:** Surveyors complete jobs, data syncs to team dashboard.
3. **Review & QA:** Office staff review and edit reports as needed.
4. **Client Delivery:** Reports delivered to clients, with branding and custom sections.

### 3.2 Data Flow

- **Mobile/PC Device → Cloud Sync → Team Dashboard → Client**
- Data is captured on-site or at the office, synced to the cloud, and made available for team review and client delivery.

---

## 4. Future Features & Roadmap (Inferred)

- **Expanded Template Library:** Continued addition of new survey types and compliance templates.
- **Deeper Integrations:** Potential for integration with property management, CRM, and compliance systems.
- **Enhanced Analytics:** More advanced reporting and analytics for asset and compliance management.
- **API/Webhooks:** Possible future support for integrations and automation.

---

## 5. Customer Feedback & Sentiment

### 5.1 Testimonials

- **Consistent Praise:** Customers highlight time savings, ease of use, flexibility, and excellent support.
- **Business Impact:** Reports of increased efficiency, reduced admin time, and improved profitability.
- **Support:** Strong, responsive support team frequently mentioned in reviews.

### 5.2 Weaknesses (from site and competitive analysis)

- **Complexity:** High degree of customization may be overwhelming for some users.
- **UI Modernity:** Interface is functional but may lack the polish of newer, mobile-first competitors.
- **Learning Curve:** Custom template designer and feature toggles may require training.
- **No Mention of Advanced Integrations:** Lacks deep integrations with third-party systems out of the box.

---

## 6. Visual Model – PocketSurvey Workflow

```mermaid
flowchart TD
    A["Select Template"] --> B["Create Job"]
    B --> C["On-Site Data Capture"]
    C --> D["Photo & Media Capture"]
    D --> E["Offline Data Storage"]
    E --> F["Automatic Sync (Cloud)"]
    F --> G["Generate PDF Report"]
    G --> H["Send to Client"]
    F --> I["Team Review (if multi-user)"]
    I --> G
```

---

## 7. Competitive Positioning

### 7.1 Strengths

- **Template Flexibility:** Highly customizable templates for any survey type.
- **Multi-Platform:** Works on both mobile and desktop; reports can be generated anywhere.
- **Comprehensive Library:** Wide range of ready-to-go compliance and property templates.
- **Team Features:** Built-in calendar, job progress, and team management.
- **Customer Support:** Responsive, knowledgeable support team.

### 7.2 Weaknesses

- **Complexity:** Customization options may be daunting for new users.
- **UI/UX:** May not match the modern, mobile-first experience of some competitors.
- **Integrations:** Limited out-of-the-box integrations with external systems.

---

## 8. Strategic Recommendations for Strata Compliance

- **Balance Flexibility & Simplicity:** Offer powerful customization, but with guided setup and best-practice templates.
- **Modern Mobile UX:** Prioritize a polished, intuitive mobile experience.
- **Progressive Onboarding:** Help users get value quickly, then unlock advanced features as needed.
- **API-First:** Build for easy integration with property management and compliance systems.
- **Transparent Pricing:** Keep entry costs low and pricing clear.

---

## 9. Supporting Diagrams

### 9.1 Feature Map

```mermaid
graph TD
    A["PocketSurvey"]
    A --> B["Survey Creation"]
    A --> C["Report Generation"]
    A --> D["Job Management"]
    A --> E["Client Management"]
    A --> F["Team Collaboration"]
    B --> B1["Ready-to-Go Templates"]
    B --> B2["Custom Survey Designer"]
    B --> B3["Mobile Data Entry"]
    B --> B4["Photo Capture"]
    C --> C1["Instant PDF Reports"]
    C --> C2["Custom Branding"]
    D --> D1["Jobs Calendar"]
    D --> D2["Progress Tracking"]
    E --> E1["Buildings List"]
    E --> E2["Building Details"]
    F --> F1["Multi-User Sync"]
    F --> F2["Role-Based Access"]
```

---

## 10. References

- [PocketSurvey Website](https://www.pocketsurvey.com/index.htm)
- [Competitive Feature Analysis – Strata Compliance](docs/competitive-feature-analysis.md)
