# User Story Task Breakdown Instructions

## Objective
Break down all remaining user stories (003 onwards) in `docs/product/requirements/user-stories/` into discrete, manageable tasks following the established pattern from use case 002.

## Background Context
Use case 001 is complete. Use case 002 has been broken down into 16 tasks (002-001 through 002-016) as a reference pattern. You need to complete the breakdown for use cases 003 through 012.

## Task Granularity Requirements

### Size and Scope
- Each task should be completable by a **single agent run in 2-3 minutes**
- Development time should be **1-2 hours for core work** (2-4 hours including tests, documentation)
- Tasks must include **all deliverables**: frontend components, API endpoints, validation, tests, documentation
- If a task appears too large or complex, **split it further** to prevent context exhaustion

### Technical vs Functional Balance
- Tasks are **primarily functional** but include necessary technical decisions
- Acknowledge the **established tech stack**: React/Ionic for mobile, Supabase for backend, TypeScript throughout
- Include **technical decision-making tasks** where architectural choices are needed
- Focus on **deliverable outcomes** rather than implementation details

## File Structure Requirements

### Directory Organization
```
docs/product/requirements/user-stories/
├── 003-client-management/
│   ├── 003-001.md
│   ├── 003-002.md
│   ├── 003-xxx.md
│   └── README.md
├── 004-site-management/
│   ├── 004-001.md
│   ├── 004-002.md
│   └── README.md
└── [continue for all use cases]
```

### Task Naming Convention
- Use **full parent-child numbering**: 003-001, 003-002, etc. (not just 001, 002)
- Tasks within each use case start from 001 and increment sequentially
- Maintain numbering even if tasks are added later

## Required Task File Structure

Each task file (e.g., 003-001.md) must contain:

### Header Section
```markdown
# Task XXX-YYY: [Clear, Action-Oriented Title]

**Parent Use Case:** XXX-[use-case-name]  
**Task ID:** XXX-YYY  
**Title:** [Same as header]  
**Estimated Development Time:** X-Y hours (including tests/docs)  
**Estimated Agent Time:** X-Y minutes  
```

### Core Sections (Required)
1. **Description** - Context and purpose of the task
2. **Acceptance Criteria** - Specific, testable requirements (checkbox list)
3. **Deliverables Checklist** - Detailed breakdown of what must be built (checkbox list)
4. **Dependencies** - What's required before starting, what this blocks
5. **Technical Considerations** - Architecture, performance, security, mobile considerations
6. **Definition of Done** - Final checklist for task completion

### Optional Sections (Include as relevant)
- **Notes** - Important context, business considerations, future planning
- **Integration Points** - How this connects to other features
- **User Experience Considerations** - Mobile UX, accessibility, professional appearance

## Task Categories and Patterns

### Foundation Tasks (Always Required)
1. **Data Model** - Database schema, TypeScript interfaces, RLS policies
2. **Core Component** - Main React component for the feature
3. **API Endpoints** - Backend CRUD operations, validation, security

### Feature Implementation Tasks
- Individual form components and validation
- File upload/management components
- Search and filtering functionality
- Specialized business logic components

### User Experience Tasks
- Progress tracking and guidance
- Offline functionality and sync
- Edit/update functionality
- Display/view components

### Integration Tasks
- Form integration/orchestration
- Navigation integration
- Settings integration
- Onboarding/tutorial components

### Quality Assurance (Always Required)
- **Integration Testing and Documentation** - Final task ensuring production readiness

## Dependency Management

### Dependency Types
- **Required Before Starting** - Must be complete before this task begins
- **Blocks These Tasks** - Tasks that cannot start until this is complete
- **Integration Dependencies** - Tasks that need coordination but not strict ordering

### Common Dependency Patterns
- Data models always come first
- API endpoints depend on data models
- Form components depend on data models and often APIs
- Integration tasks depend on individual components
- Testing/documentation tasks come last

### Cross-Use-Case Dependencies
- Consider dependencies between different use cases (e.g., client management may depend on business profile)
- Note these clearly in the Dependencies section

## Technical Considerations Guidelines

### Always Include
- **Mobile optimization** requirements and considerations
- **Offline functionality** needs for field workers
- **UK-specific requirements** (addresses, regulations, standards)
- **Security considerations** (RLS policies, validation, file handling)
- **Performance requirements** for mobile devices and slow connections

### Architecture Alignment
- **Multi-tenant RLS policies** with tenant_id isolation
- **Role-based permissions** (TenantAdmin, Scheduler, Inspector)
- **Mobile-first design** with Ionic components
- **API-only database access** for mobile security
- **Supabase integration** patterns established in the codebase

## Quality Standards

### Completeness Check
For each use case, ensure tasks cover:
- [ ] Complete data model and schema
- [ ] All user-facing functionality from acceptance criteria
- [ ] API endpoints for all operations
- [ ] Mobile-optimized user interface
- [ ] Offline capability where relevant
- [ ] Integration with app navigation and settings
- [ ] Comprehensive testing and documentation

### Gap Analysis
Compare your task breakdown against the original use case acceptance criteria to ensure:
- Every acceptance criterion is addressed by at least one task
- No functionality is missing
- Additional tasks are included for complete application functionality (navigation, integration, etc.)

## Use Case Priority Order
Work through use cases in this order:
1. 003-client-management
2. 004-site-management  
3. 005-inspection-creation
4. 006-offline-inspection-workflow
5. 007-photo-media-capture
6. 008-inspection-findings
7. 009-basic-reporting
8. 010-report-sharing
9. 011-basic-scheduling
10. 012-data-sync-backup

## Reference Example
Use the completed breakdown in `docs/product/requirements/user-stories/002-business-profile-setup/` as your reference for:
- File structure and naming
- Content depth and detail
- Technical consideration depth
- Dependency mapping patterns
- Task sizing and scope

## Deliverable Requirements
For each use case (003-012), create:
- [ ] Subfolder with use case name
- [ ] Individual task files (XXX-001.md, XXX-002.md, etc.)
- [ ] README.md with task overview, dependency flow, and timeline
- [ ] Ensure all tasks together deliver a complete, functioning feature

## Success Criteria
When complete, each use case should have:
- **8-20 discrete tasks** (depending on complexity)
- **Clear dependency chain** from foundation to integration
- **Complete feature coverage** addressing all acceptance criteria
- **Production-ready scope** including testing and documentation
- **Agent-executable tasks** with full context and clear deliverables

Begin with use case 003-client-management and work systematically through each use case. Do not ask questions - use the patterns established in 002 and apply them consistently across all remaining use cases.
