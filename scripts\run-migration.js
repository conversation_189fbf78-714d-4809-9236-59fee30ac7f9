#!/usr/bin/env node

/**
 * Migration runner script for Supabase
 * Executes SQL migration files using the service role key
 */

const {
  createClient,
} = require("../packages/core/node_modules/@supabase/supabase-js");
const fs = require("fs");
const path = require("path");

// Load environment variables manually since dotenv might not be available
const envPath = path.join(__dirname, "..", ".env.local");
const envContent = fs.readFileSync(envPath, "utf8");
const envLines = envContent.split("\n");

// Parse environment variables
const env = {};
envLines.forEach((line) => {
  const [key, ...valueParts] = line.split("=");
  if (key && valueParts.length > 0) {
    env[key.trim()] = valueParts.join("=").trim();
  }
});

// Set environment variables
Object.assign(process.env, env);

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !serviceRoleKey) {
  console.error("❌ Missing Supabase credentials in .env.local");
  console.error(
    "Required: NEXT_PUBLIC_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY"
  );
  process.exit(1);
}

// Create Supabase client with service role key (bypasses RLS)
const supabase = createClient(supabaseUrl, serviceRoleKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false,
  },
});

async function runMigration(migrationFile) {
  console.log(`🔄 Running migration: ${migrationFile}`);

  try {
    // Read the migration file
    const migrationPath = path.join(
      __dirname,
      "..",
      "database",
      "migrations",
      migrationFile
    );
    const sql = fs.readFileSync(migrationPath, "utf8");

    console.log(`📄 Executing SQL from ${migrationFile}...`);

    // Execute the SQL
    const { data, error } = await supabase.rpc("exec_sql", { sql_query: sql });

    if (error) {
      // If exec_sql doesn't exist, try direct query
      if (error.code === "42883") {
        console.log("📝 Using direct SQL execution...");

        // Split SQL into individual statements and execute them
        const statements = sql
          .split(";")
          .map((stmt) => stmt.trim())
          .filter((stmt) => stmt.length > 0 && !stmt.startsWith("--"));

        for (const statement of statements) {
          if (statement.trim()) {
            console.log(`   Executing: ${statement.substring(0, 50)}...`);
            const { error: stmtError } = await supabase.rpc("exec", {
              sql: statement,
            });
            if (stmtError) {
              console.error(
                `❌ Error executing statement: ${stmtError.message}`
              );
              throw stmtError;
            }
          }
        }
      } else {
        throw error;
      }
    }

    console.log(`✅ Migration ${migrationFile} completed successfully`);
    return true;
  } catch (error) {
    console.error(`❌ Migration ${migrationFile} failed:`, error.message);
    return false;
  }
}

async function verifyTable() {
  console.log("🔍 Verifying business_profiles table...");

  try {
    // Check if table exists and get its structure
    const { data, error } = await supabase
      .from("business_profiles")
      .select("*")
      .limit(0);

    if (error) {
      console.error("❌ Table verification failed:", error.message);
      return false;
    }

    console.log("✅ business_profiles table exists and is accessible");

    // Check RLS is enabled
    const { data: rlsData, error: rlsError } = await supabase.rpc("check_rls", {
      table_name: "business_profiles",
    });

    if (!rlsError && rlsData) {
      console.log("✅ RLS policies are configured");
    } else {
      console.log("⚠️  Could not verify RLS status (this is normal)");
    }

    return true;
  } catch (error) {
    console.error("❌ Table verification error:", error.message);
    return false;
  }
}

async function main() {
  console.log("🚀 Starting business_profiles migration...");
  console.log(`📡 Connecting to: ${supabaseUrl}`);

  // Run the migration
  const success = await runMigration("006_create_business_profiles.sql");

  if (success) {
    // Verify the table was created
    const verified = await verifyTable();

    if (verified) {
      console.log("🎉 Migration completed successfully!");
      console.log("📋 Next steps:");
      console.log("   1. Verify table structure in Supabase dashboard");
      console.log("   2. Test RLS policies with sample data");
      console.log("   3. Update task checklist as complete");
    } else {
      console.log("⚠️  Migration ran but verification failed");
    }
  } else {
    console.log("💥 Migration failed - check errors above");
    process.exit(1);
  }
}

// Run the migration
main().catch(console.error);
