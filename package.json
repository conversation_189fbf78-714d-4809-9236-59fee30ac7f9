{"name": "strata-compliance", "private": true, "scripts": {"build": "turbo run build", "dev": "turbo run dev", "lint": "turbo run lint", "test": "turbo run test", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:headed": "playwright test --headed", "test:e2e:debug": "playwright test --debug", "test:e2e:report": "playwright show-report", "format": "prettier --write \"**/*.{ts,tsx,md}\""}, "devDependencies": {"@playwright/test": "^1.54.1", "@typescript-eslint/eslint-plugin": "^8.34.1", "@typescript-eslint/parser": "^8.34.1", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "prettier": "^3.2.5", "turbo": "^2.5.4", "typescript": "^5.3.3"}, "packageManager": "pnpm@8.15.4", "engines": {"node": ">=18.0.0"}}