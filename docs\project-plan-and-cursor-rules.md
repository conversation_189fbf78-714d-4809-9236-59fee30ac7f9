# Strata Compliance – Turborepo Mono‑repo Blueprint

> _This file is the single source of truth for humans **and** the Cursor AI Agent. Keep it in /docs so the Agent always has access._

---

## 1 Product requirements (v0.3)

### 1.1 Distribution & market strategy

- **App store first**
  - Primary distribution via iOS App Store and Google Play Store as free advertising channel.
  - Mobile app designed for complete business operation without desktop dependency.
  - Single worker companies should be able to download and immediately start managing their business.

### 1.2 Functional scope

- **Mobile-first job lifecycle**

  - **Solo workers**: Create inspections directly in mobile app, capture findings/photos/signatures, generate and share reports with clients.
  - **Team setups**: Office staff create inspection jobs, assign inspectors & due dates via desktop (premium tier).
  - Mobile inspectors download jobs, work **offline**, capture findings, photos & signatures, then sync.
  - Office reviews submissions (desktop), raises follow‑up actions, generates PDF/HTML reports, publishes to the client portal.

- **Mobile data management**

  - Create and manage client database from mobile app.
  - Add and maintain site information with location data.
  - Permission-based data creation (may be restricted in team environments based on user role).

- **Subscription tiers & capabilities**

  - **Basic (Mobile-only)**: Solo worker can create inspections, manage clients/sites, issue basic reports.
  - **Professional**: Enhanced mobile features, basic client portal access.
  - **Enterprise**: Full desktop access with advanced scheduling, team management, messaging, enhanced reporting.
  - **Team**: Multi-user support with role-based permissions and invitation system.

- **User onboarding paths**

  - **Individual path**: Download app → create account → start creating inspections immediately.
  - **Enterprise path**: Admin registers with subscription → surveyors receive invitation codes → download app → join organization.

- **Scheduling**

  - **Mobile**: Basic scheduling and calendar view for solo workers.
  - **Desktop** (Enterprise tier): Advanced drag‑drop calendar, route mapping, bulk reschedule operations.
  - Recurring jobs, overdue alerts and Google/Outlook calendar export (ICS).

- **Data model**

  - Multi‑tenant → Clients → Sites → Assets hierarchy.
  - Upload & version site plans (PDF/SVG) with floor coordinates for map overlay.
  - Retain historical reports & documents for 7 years (configurable).

- **Client portal**

  - Read‑only access to Sites, upcoming due dates, published reports, outstanding remedial actions.
  - Report viewing accessible via mobile-generated links for solo workers.

- **Feature development priority**

  - **Phase 1 (MVP)**: Mobile app with basic inspection creation, client/site management, simple reporting.
  - **Phase 2**: Enhanced mobile features, basic client portal, subscription tiers.
  - **Phase 3**: Desktop application for enterprise users, advanced scheduling, team management.
  - **Phase 4**: Advanced integrations, messaging, enhanced reporting, enterprise features.

- **Roles & permissions**

  - `TenantAdmin`, `Scheduler`, `Inspector`, `ClientUser`, `SoloWorker`; enforced via Supabase RLS + `role_permissions` table.
  - Data creation permissions configurable per role (clients, sites, inspections).
  - Team invitation and code-based onboarding system.

- **Notifications**
  - Email (Supabase SMTP) + optional push (FCM/APNS) for new jobs and approaching deadlines.

### 1.3 Core strategy principles

- **Mobile-first development**: All core business functions must be available on mobile before desktop features are considered.
- **Progressive enhancement**: Solo workers should achieve complete business value from mobile app alone.
- **Subscription-gated features**: Desktop and advanced features unlock higher value capabilities and revenue tiers.
- **Minimal viable onboarding**: New users should be productive within minutes of app download.
- **App store optimization**: Design and feature decisions should consider app store discovery and conversion.

### 1.4 Non‑functional targets

- **Zero‑to‑one cost:** £0 cloud bill for first **50 tenants / 10 k monthly requests**.
- **Offline resilience:** mobile app must function 24 h without connectivity (IndexedDB cache, background sync).
- **Performance:** <200 ms median API latency (95th <500 ms); Lighthouse mobile ≥ 90.
- **Security & compliance:**

  - **OWASP ASVS L2**, CSP locked, Snyk scan in CI.
  - GDPR export & delete endpoints; encryption‑at‑rest via Supabase.
  - Audit trail on all DB mutations (trigger into `audit_log`).

- **Scalability:** free tier → 10 k MAU; path to >100 k with Neon read replicas / Cloud Run workers.
- **Quality:** 95 % unit test coverage on `packages/core`; 80 % integration; Playwright E2E suite green.
- **Availability:** target SLA 99.9 % (edge‑served static, multi‑region Supabase read replicas later).

---

## 2 Architecture snapshot

```
Ionic PWA      React + MUI desktop
      \           /
       Cloudflare Pages  (static)
                 |
           Pages Functions / Workers  (BFF, TypeScript)
                 |
          Supabase  (Postgres + Storage, RLS)
```

_Container / .NET services may be added later via Cloud Run._

## 3 Tech‑stack choices

| Concern    | Choice                                                | Rationale                                                                                                                                                                          |
| ---------- | ----------------------------------------------------- | ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| Monorepo   | **Turborepo**                                         | Native Cloudflare support via custom deploy commands ([developers.cloudflare.com](https://developers.cloudflare.com/workers/ci-cd/builds/advanced-setups/?utm_source=chatgpt.com)) |
| Mobile     | **Ionic React**                                       | Web‑components, Capacitor; paired with Split‑Pane for desktop responsive                                                                                                           |
| Desktop    | **React 19 + MUI v6 + FullCalendar Premium**          | Data‑dense widgets, Material look‑and‑feel                                                                                                                                         |
| API        | **Cloudflare Pages Functions (Workers runtime)** – TS | Global edge, 100 k free req/day, first‑class TS types ([docs.cursor.com](https://docs.cursor.com/context/rules?utm_source=chatgpt.com))                                            |
| DB & files | **Supabase** Free tier                                | RLS, Auth, Storage bundle                                                                                                                                                          |
| Build      | pnpm 8 + Turborepo – remote cache                     |                                                                                                                                                                                    |

## 4 Repo layout

```
apps/
  mobile/      # Ionic
  desktop/     # React+MUI
  api-bff/     # Worker
packages/
  core/        # domain logic, DTOs, validators
  ui/          # shared design‑tokens, atoms
  auth/        # Supabase client wrapper
infra/         # wrangler.toml, CF Pages config, GitHub Actions
```

## 5 Coding standards

- **TypeScript strict** – no `any` (rule enforced in `.eslintrc`).
- React function components; hooks over classes.
- Conventional Commits (`feat:`, `fix:` …).
- Write tests **first**, then code, repeat until green – proven to keep AI on track ([builder.io](https://www.builder.io/blog/cursor-tips?utm_source=chatgpt.com)).

## 6 Testing strategy

| Layer  | Tool                                           |
| ------ | ---------------------------------------------- |
| Unit   | vitest + jsdom                                 |
| API    | Playwright request tests against dev Worker    |
| E2E    | Playwright mobile‑emulation + desktop‑viewport |
| Schema | sqlc static analysis + Supabase CI shadow DB   |

## 7 CI / CD (GitHub Actions)

1. `pnpm install --frozen-lockfile`
2. `turbo run lint test build --cache-dir=.turbo`

   - `mobile:build` → Pages artefact `/public-mobile`
   - `desktop:build` → Pages artefact `/public-desktop`
   - `api-bff:deploy` → `wrangler pages functions deploy`

3. Deploy to Cloudflare using Environment Variables set in repo secrets.

## 8 Security & secrets

- Service‑role key stored in **CF Pages Project > Variables (encrypted)**; _never_ in code.
- Supabase JWT forwarded by Worker so RLS applies (pattern in `/apps/api-bff/lib/supabase.ts`).
- Rate‑limit per `tenant_id`.

## 9 Cursor collaboration protocol

The `.cursorrules` below is auto‑loaded by Cursor. It distils best practice from the Cursor docs and community tips ([docs.cursor.com](https://docs.cursor.com/context/rules?utm_source=chatgpt.com), [reddit.com](https://www.reddit.com/r/ChatGPTCoding/comments/1hu276s/how_to_use_cursor_more_efficiently/?utm_source=chatgpt.com)).

```text
# .cursorrules – keep at repo root ########################################
Role: You are an **expert full‑stack TypeScript developer** building Strata Compliance.
Knowledge: Always consult /docs/project-plan-and-cursor-rules.md before acting.
Process:
  • Work in **tiny pull‑request‑sized changes** (≤ 200 LOC).
  • When a task is vague, **ask the user** to clarify before changing code.
  • Write or update **tests first**, run them, then modify code until tests pass.
  • If a change spans multiple packages/apps, plan the sequence and confirm.
  • Never introduce `any`; use strict types.
  • Use pnpm scripts and Turborepo tasks – don't invent new scripts.
  • Use Supabase client wrapper from `packages/auth` – no direct `fetch`.
  • Commit messages must follow Conventional Commits.
  • After edits, run `turbo run lint test` locally – CI must stay green.
Limitations:
  • Do **not** assume un‑specified requirements; surface options instead.
  • Cloudflare Workers runtime only supports Fetch APIs – avoid Node fs/net.
  • Stay within free‑tier quotas unless user approves cost increase.
###########################################################################
```

> **Updating this document**: suggest edits via PR titled `docs: update blueprint`; merge after review.

---

### Next step

Commit this file, add `.cursorrules` with the same block, then initialise Turborepo with `pnpm dlx turbo@latest init`. Cursor can now operate safely inside these guard‑rails.

## Project Organization & Documentation Index

### 📋 Essential Reading Order for New Agents

1. **This file** (`docs/project-plan-and-cursor-rules.md`) - Core project requirements and tech stack
2. **Implementation Status** (`docs/implementation-checklist.md`) - What's done, what's next
3. **Strategic Context** (`docs/user-functionality-analysis.md`) - Market analysis and user requirements
4. **Technical Architecture** (`docs/technical-architecture-plan.md`) - Aligned with this project plan
5. **Organization Guidelines** (`docs/project-organization.md`) - Code/docs standards

### 🎯 Strategic Planning Documents

- **User & Market Analysis**: `docs/user-functionality-analysis.md` - Solo workers, teams, competitive landscape
- **Competitive Features**: `docs/competitive-feature-analysis.md` - How competitors handle workflows
- **License Strategy**: `docs/license-tier-strategy.md` - Basic/Professional/Enterprise tiers with client portals
- **Pricing Strategy**: `docs/pricing-strategy-analysis.md` - Per-seat licensing, enterprise value capture
- **User Journeys**: `docs/user-journey-flows.md` - Detailed workflows for each tier
- **Implementation Roadmap**: `docs/implementation-roadmap.md` - 18-month development plan

### 🔧 Technical Implementation

- **Architecture Plan**: `docs/technical-architecture-plan.md` - **ALIGNED WITH THIS PROJECT PLAN**
- **Architecture Alignment**: `docs/architecture-alignment-summary.md` - Corrections made to align with existing decisions
- **Database Setup**: `database/SETUP.md` - Complete database setup instructions
- **Existing Codebase**: Check existing implementations before creating new ones

### 🖼️ Design References

- **Spotlite Screenshots**: `docs/spotlite/` - Existing single-tenant product inspiring webtop design
- **Interface Analysis**: `docs/spotlite/index.html` - Organized view of Spotlite interfaces

### ⚠️ Critical Constraints & Decisions

- **Technology Stack**: Ionic React + Cloudflare Workers + Supabase (FIXED - do not change)
- **Architecture**: Multi-tenant RLS, not custom schema (FIXED - do not change)
- **Offline Requirement**: 24h operation with IndexedDB cache (FIXED - do not change)
- **Cost Target**: £0 for first 50 tenants using free tiers (FIXED - do not change)
