// Database type definitions for Strata Compliance

export type DatabaseJson = {
  public: {
    Tables: {
      tenants: {
        Row: {
          id: string;
          created_at: string;
          updated_at: string;
          name: string;
          slug: string;
          logo_url: string | null;
          subscription_tier: "free" | "pro" | "enterprise";
          max_users: number;
          max_clients: number;
          timezone: string;
          date_format: string;
          currency: string;
          status: "active" | "suspended" | "archived";
          archived_at: string | null;
          archived_by: string | null;
        };
        Insert: {
          id?: string;
          created_at?: string;
          updated_at?: string;
          name: string;
          slug: string;
          logo_url?: string | null;
          subscription_tier?: "free" | "pro" | "enterprise";
          max_users?: number;
          max_clients?: number;
          timezone?: string;
          date_format?: string;
          currency?: string;
          status?: "active" | "suspended" | "archived";
          archived_at?: string | null;
          archived_by?: string | null;
        };
        Update: {
          id?: string;
          created_at?: string;
          updated_at?: string;
          name?: string;
          slug?: string;
          logo_url?: string | null;
          subscription_tier?: "free" | "pro" | "enterprise";
          max_users?: number;
          max_clients?: number;
          timezone?: string;
          date_format?: string;
          currency?: string;
          status?: "active" | "suspended" | "archived";
          archived_at?: string | null;
          archived_by?: string | null;
        };
      };
      tenant_users: {
        Row: {
          id: string;
          created_at: string;
          updated_at: string;
          tenant_id: string;
          user_id: string;
          role: "TenantAdmin" | "Scheduler" | "Inspector" | "ClientUser";
          permissions: Record<string, boolean | string | number>;
          first_name: string | null;
          last_name: string | null;
          position: string | null;
          office_phone: string | null;
          mobile_phone: string | null;
          signature_url: string | null;
          profile_image_url: string | null;
          status: "active" | "inactive" | "suspended";
          invited_at: string | null;
          activated_at: string | null;
          last_login_at: string | null;
        };
        Insert: {
          id?: string;
          created_at?: string;
          updated_at?: string;
          tenant_id: string;
          user_id: string;
          role: "TenantAdmin" | "Scheduler" | "Inspector" | "ClientUser";
          permissions?: Record<string, boolean | string | number>;
          first_name?: string | null;
          last_name?: string | null;
          position?: string | null;
          office_phone?: string | null;
          mobile_phone?: string | null;
          signature_url?: string | null;
          profile_image_url?: string | null;
          status?: "active" | "inactive" | "suspended";
          invited_at?: string | null;
          activated_at?: string | null;
          last_login_at?: string | null;
        };
        Update: {
          id?: string;
          created_at?: string;
          updated_at?: string;
          tenant_id?: string;
          user_id?: string;
          role?: "TenantAdmin" | "Scheduler" | "Inspector" | "ClientUser";
          permissions?: Record<string, boolean | string | number>;
          first_name?: string | null;
          last_name?: string | null;
          position?: string | null;
          office_phone?: string | null;
          mobile_phone?: string | null;
          signature_url?: string | null;
          profile_image_url?: string | null;
          status?: "active" | "inactive" | "suspended";
          invited_at?: string | null;
          activated_at?: string | null;
          last_login_at?: string | null;
        };
      };
      clients: {
        Row: {
          id: string;
          created_at: string;
          updated_at: string;
          tenant_id: string;
          name: string;
          url_safe_name: string | null;
          logo_url: string | null;
          client_type: number;
          emergency_contact_details: string | null;
          escalation_procedure: string | null;
          kpis_and_slas: string | null;
          invoice_email_address: string | null;
          account_queries_email_address: string | null;
          general_requirements: string | null;
          building_name: string | null;
          building_number: string | null;
          unit: string | null;
          floor: string | null;
          status: "active" | "inactive" | "archived";
          archived_at: string | null;
          archived_by: string | null;
          archive_reason: string | null;
        };
        Insert: {
          id?: string;
          created_at?: string;
          updated_at?: string;
          tenant_id: string;
          name: string;
          url_safe_name?: string | null;
          logo_url?: string | null;
          client_type?: number;
          emergency_contact_details?: string | null;
          escalation_procedure?: string | null;
          kpis_and_slas?: string | null;
          invoice_email_address?: string | null;
          account_queries_email_address?: string | null;
          general_requirements?: string | null;
          building_name?: string | null;
          building_number?: string | null;
          unit?: string | null;
          floor?: string | null;
          status?: "active" | "inactive" | "archived";
          archived_at?: string | null;
          archived_by?: string | null;
          archive_reason?: string | null;
        };
        Update: {
          id?: string;
          created_at?: string;
          updated_at?: string;
          tenant_id?: string;
          name?: string;
          url_safe_name?: string | null;
          logo_url?: string | null;
          client_type?: number;
          emergency_contact_details?: string | null;
          escalation_procedure?: string | null;
          kpis_and_slas?: string | null;
          invoice_email_address?: string | null;
          account_queries_email_address?: string | null;
          general_requirements?: string | null;
          building_name?: string | null;
          building_number?: string | null;
          unit?: string | null;
          floor?: string | null;
          status?: "active" | "inactive" | "archived";
          archived_at?: string | null;
          archived_by?: string | null;
          archive_reason?: string | null;
        };
      };
      sites: {
        Row: {
          id: string;
          created_at: string;
          updated_at: string;
          tenant_id: string;
          client_id: string;
          name: string;
          description: string | null;
          site_code: string | null;
          address_line_1: string | null;
          address_line_2: string | null;
          city: string | null;
          state_province: string | null;
          postal_code: string | null;
          country: string;
          latitude: number | null;
          longitude: number | null;
          site_plan_url: string | null;
          floor_plan_urls: string[];
          status: "active" | "inactive" | "archived";
          archived_at: string | null;
          archived_by: string | null;
          archive_reason: string | null;
        };
        Insert: {
          id?: string;
          created_at?: string;
          updated_at?: string;
          tenant_id: string;
          client_id: string;
          name: string;
          description?: string | null;
          site_code?: string | null;
          address_line_1?: string | null;
          address_line_2?: string | null;
          city?: string | null;
          state_province?: string | null;
          postal_code?: string | null;
          country?: string;
          latitude?: number | null;
          longitude?: number | null;
          site_plan_url?: string | null;
          floor_plan_urls?: string[];
          status?: "active" | "inactive" | "archived";
          archived_at?: string | null;
          archived_by?: string | null;
          archive_reason?: string | null;
        };
        Update: {
          id?: string;
          created_at?: string;
          updated_at?: string;
          tenant_id?: string;
          client_id?: string;
          name?: string;
          description?: string | null;
          site_code?: string | null;
          address_line_1?: string | null;
          address_line_2?: string | null;
          city?: string | null;
          state_province?: string | null;
          postal_code?: string | null;
          country?: string;
          latitude?: number | null;
          longitude?: number | null;
          site_plan_url?: string | null;
          floor_plan_urls?: string[];
          status?: "active" | "inactive" | "archived";
          archived_at?: string | null;
          archived_by?: string | null;
          archive_reason?: string | null;
        };
      };
      properties: {
        Row: {
          id: string;
          created_at: string;
          updated_at: string;
          tenant_id: string;
          client_id: string;
          site_id: string | null;
          property_code: string | null;
          notes: string | null;
          group_name: string | null;
          building_name: string | null;
          building_number: string | null;
          unit: string | null;
          floor: string | null;
          image_container_name: string | null;
          image_blob_name: string | null;
          qr_code_id: string | null;
          qr_code_document_id: string | null;
          is_managed: boolean;
          status: "active" | "inactive" | "archived";
          archived_at: string | null;
          archived_by: string | null;
          archive_reason: string | null;
          deleted_at: string | null;
        };
        Insert: {
          id?: string;
          created_at?: string;
          updated_at?: string;
          tenant_id: string;
          client_id: string;
          site_id?: string | null;
          property_code?: string | null;
          notes?: string | null;
          group_name?: string | null;
          building_name?: string | null;
          building_number?: string | null;
          unit?: string | null;
          floor?: string | null;
          image_container_name?: string | null;
          image_blob_name?: string | null;
          qr_code_id?: string | null;
          qr_code_document_id?: string | null;
          is_managed?: boolean;
          status?: "active" | "inactive" | "archived";
          archived_at?: string | null;
          archived_by?: string | null;
          archive_reason?: string | null;
          deleted_at?: string | null;
        };
        Update: {
          id?: string;
          created_at?: string;
          updated_at?: string;
          tenant_id?: string;
          client_id?: string;
          site_id?: string | null;
          property_code?: string | null;
          notes?: string | null;
          group_name?: string | null;
          building_name?: string | null;
          building_number?: string | null;
          unit?: string | null;
          floor?: string | null;
          image_container_name?: string | null;
          image_blob_name?: string | null;
          qr_code_id?: string | null;
          qr_code_document_id?: string | null;
          is_managed?: boolean;
          status?: "active" | "inactive" | "archived";
          archived_at?: string | null;
          archived_by?: string | null;
          archive_reason?: string | null;
          deleted_at?: string | null;
        };
      };
      business_profiles: {
        Row: {
          id: string;
          created_at: string;
          updated_at: string;
          tenant_id: string;
          user_id: string;
          company_name: string;
          trading_name: string | null;
          company_number: string | null;
          vat_number: string | null;
          email: string | null;
          phone: string | null;
          mobile: string | null;
          website: string | null;
          address_line_1: string | null;
          address_line_2: string | null;
          city: string | null;
          state_province: string | null;
          postal_code: string | null;
          country: string;
          industry: string | null;
          business_type: string | null;
          professional_body_registration: string | null;
          insurance_policy_number: string | null;
          insurance_expiry_date: string | null;
          logo_url: string | null;
          brand_color: string | null;
          report_footer_text: string | null;
          default_report_template: string;
          default_currency: string;
          default_timezone: string;
          financial_year_end: string | null;
          status: "active" | "inactive" | "archived";
          archived_at: string | null;
          archived_by: string | null;
        };
        Insert: {
          id?: string;
          created_at?: string;
          updated_at?: string;
          tenant_id: string;
          user_id: string;
          company_name: string;
          trading_name?: string | null;
          company_number?: string | null;
          vat_number?: string | null;
          email?: string | null;
          phone?: string | null;
          mobile?: string | null;
          website?: string | null;
          address_line_1?: string | null;
          address_line_2?: string | null;
          city?: string | null;
          state_province?: string | null;
          postal_code?: string | null;
          country?: string;
          industry?: string | null;
          business_type?: string | null;
          professional_body_registration?: string | null;
          insurance_policy_number?: string | null;
          insurance_expiry_date?: string | null;
          logo_url?: string | null;
          brand_color?: string | null;
          report_footer_text?: string | null;
          default_report_template?: string;
          default_currency?: string;
          default_timezone?: string;
          financial_year_end?: string | null;
          status?: "active" | "inactive" | "archived";
          archived_at?: string | null;
          archived_by?: string | null;
        };
        Update: {
          id?: string;
          created_at?: string;
          updated_at?: string;
          tenant_id?: string;
          user_id?: string;
          company_name?: string;
          trading_name?: string | null;
          company_number?: string | null;
          vat_number?: string | null;
          email?: string | null;
          phone?: string | null;
          mobile?: string | null;
          website?: string | null;
          address_line_1?: string | null;
          address_line_2?: string | null;
          city?: string | null;
          state_province?: string | null;
          postal_code?: string | null;
          country?: string;
          industry?: string | null;
          business_type?: string | null;
          professional_body_registration?: string | null;
          insurance_policy_number?: string | null;
          insurance_expiry_date?: string | null;
          logo_url?: string | null;
          brand_color?: string | null;
          report_footer_text?: string | null;
          default_report_template?: string;
          default_currency?: string;
          default_timezone?: string;
          financial_year_end?: string | null;
          status?: "active" | "inactive" | "archived";
          archived_at?: string | null;
          archived_by?: string | null;
        };
      };
      inspections: {
        Row: {
          id: string;
          created_at: string;
          updated_at: string;
          tenant_id: string;
          property_id: string;
          assigned_inspector_id: string | null;
          project_id: string | null;
          inspection_type: string;
          title: string;
          description: string | null;
          scheduled_date: string | null;
          due_date: string | null;
          completed_at: string | null;
          status:
            | "draft"
            | "scheduled"
            | "in_progress"
            | "completed"
            | "requires_follow_up"
            | "approved"
            | "cancelled";
          findings: Record<string, string | number | boolean>;
          photos: string[];
          documents: string[];
          signature_data: Record<string, string | number> | null;
          follow_up_required: boolean;
          follow_up_date: string | null;
          follow_up_notes: string | null;
          reviewed_by: string | null;
          reviewed_at: string | null;
          approved_by: string | null;
          approved_at: string | null;
        };
        Insert: {
          id?: string;
          created_at?: string;
          updated_at?: string;
          tenant_id: string;
          property_id: string;
          assigned_inspector_id?: string | null;
          project_id?: string | null;
          inspection_type: string;
          title: string;
          description?: string | null;
          scheduled_date?: string | null;
          due_date?: string | null;
          completed_at?: string | null;
          status?:
            | "draft"
            | "scheduled"
            | "in_progress"
            | "completed"
            | "requires_follow_up"
            | "approved"
            | "cancelled";
          findings?: Record<string, string | number | boolean>;
          photos?: string[];
          documents?: string[];
          signature_data?: Record<string, string | number> | null;
          follow_up_required?: boolean;
          follow_up_date?: string | null;
          follow_up_notes?: string | null;
          reviewed_by?: string | null;
          reviewed_at?: string | null;
          approved_by?: string | null;
          approved_at?: string | null;
        };
        Update: {
          id?: string;
          created_at?: string;
          updated_at?: string;
          tenant_id?: string;
          property_id?: string;
          assigned_inspector_id?: string | null;
          project_id?: string | null;
          inspection_type?: string;
          title?: string;
          description?: string | null;
          scheduled_date?: string | null;
          due_date?: string | null;
          completed_at?: string | null;
          status?:
            | "draft"
            | "scheduled"
            | "in_progress"
            | "completed"
            | "requires_follow_up"
            | "approved"
            | "cancelled";
          findings?: Record<string, string | number | boolean>;
          photos?: string[];
          documents?: string[];
          signature_data?: Record<string, string | number> | null;
          follow_up_required?: boolean;
          follow_up_date?: string | null;
          follow_up_notes?: string | null;
          reviewed_by?: string | null;
          reviewed_at?: string | null;
          approved_by?: string | null;
          approved_at?: string | null;
        };
      };
    };
    Views: {};
    Functions: {};
    Enums: {};
    CompositeTypes: {};
  };
};

// Utility types for common operations
export type Tenant = DatabaseJson["public"]["Tables"]["tenants"]["Row"];
export type TenantInsert =
  DatabaseJson["public"]["Tables"]["tenants"]["Insert"];
export type TenantUpdate =
  DatabaseJson["public"]["Tables"]["tenants"]["Update"];

export type TenantUser =
  DatabaseJson["public"]["Tables"]["tenant_users"]["Row"];
export type TenantUserInsert =
  DatabaseJson["public"]["Tables"]["tenant_users"]["Insert"];
export type TenantUserUpdate =
  DatabaseJson["public"]["Tables"]["tenant_users"]["Update"];

export type Client = DatabaseJson["public"]["Tables"]["clients"]["Row"];
export type ClientInsert =
  DatabaseJson["public"]["Tables"]["clients"]["Insert"];
export type ClientUpdate =
  DatabaseJson["public"]["Tables"]["clients"]["Update"];

export type Site = DatabaseJson["public"]["Tables"]["sites"]["Row"];
export type SiteInsert = DatabaseJson["public"]["Tables"]["sites"]["Insert"];
export type SiteUpdate = DatabaseJson["public"]["Tables"]["sites"]["Update"];

export type Property = DatabaseJson["public"]["Tables"]["properties"]["Row"];
export type PropertyInsert =
  DatabaseJson["public"]["Tables"]["properties"]["Insert"];
export type PropertyUpdate =
  DatabaseJson["public"]["Tables"]["properties"]["Update"];

export type Inspection = DatabaseJson["public"]["Tables"]["inspections"]["Row"];
export type InspectionInsert =
  DatabaseJson["public"]["Tables"]["inspections"]["Insert"];
export type InspectionUpdate =
  DatabaseJson["public"]["Tables"]["inspections"]["Update"];

export type BusinessProfile =
  DatabaseJson["public"]["Tables"]["business_profiles"]["Row"];
export type BusinessProfileInsert =
  DatabaseJson["public"]["Tables"]["business_profiles"]["Insert"];
export type BusinessProfileUpdate =
  DatabaseJson["public"]["Tables"]["business_profiles"]["Update"];

// Role-based types
export type UserRole = "TenantAdmin" | "Scheduler" | "Inspector" | "ClientUser";
export type InspectionStatus =
  | "draft"
  | "scheduled"
  | "in_progress"
  | "completed"
  | "requires_follow_up"
  | "approved"
  | "cancelled";
export type EntityStatus = "active" | "inactive" | "archived";
