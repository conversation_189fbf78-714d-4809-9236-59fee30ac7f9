-- Create business_profiles table for tenant business information
CREATE TABLE business_profiles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    
    -- Multi-tenancy
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    
    -- Core business information
    company_name TEXT NOT NULL,
    trading_name TEXT, -- Optional trading/DBA name
    company_number TEXT, -- Companies House number (UK)
    vat_number TEXT, -- VAT registration number
    
    -- Contact information
    email TEXT,
    phone TEXT,
    mobile TEXT,
    website TEXT,
    
    -- Address information
    address_line_1 TEXT,
    address_line_2 TEXT,
    city TEXT,
    state_province TEXT,
    postal_code TEXT,
    country TEXT DEFAULT 'United Kingdom',
    
    -- Professional details
    industry TEXT,
    business_type TEXT, -- e.g., 'sole_trader', 'limited_company', 'partnership'
    professional_body_registration TEXT, -- For compliance professionals
    insurance_policy_number TEXT,
    insurance_expiry_date DATE,
    
    -- Branding
    logo_url TEXT,
    brand_color TEXT, -- Hex color for reports/branding
    
    -- Report settings
    report_footer_text TEXT,
    default_report_template TEXT DEFAULT 'standard',
    
    -- Business settings
    default_currency TEXT DEFAULT 'GBP',
    default_timezone TEXT DEFAULT 'Europe/London',
    financial_year_end DATE,
    
    -- Status and audit
    status TEXT DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'archived')),
    archived_at TIMESTAMPTZ,
    archived_by UUID REFERENCES auth.users(id),
    
    -- Constraints
    UNIQUE(tenant_id), -- One business profile per tenant
    CONSTRAINT valid_email CHECK (email IS NULL OR email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$'),
    CONSTRAINT valid_website CHECK (website IS NULL OR website ~* '^https?://'),
    CONSTRAINT valid_brand_color CHECK (brand_color IS NULL OR brand_color ~* '^#[0-9A-Fa-f]{6}$')
);

-- RLS policies
ALTER TABLE business_profiles ENABLE ROW LEVEL SECURITY;

-- Users can view their own tenant's business profile
CREATE POLICY "Users can view their tenant business profile"
    ON business_profiles FOR SELECT
    USING (tenant_id = (auth.jwt() ->> 'tenant_id')::uuid);

-- Tenant admins can create business profiles
CREATE POLICY "Tenant admins can create business profile"
    ON business_profiles FOR INSERT
    WITH CHECK (
        tenant_id = (auth.jwt() ->> 'tenant_id')::uuid 
        AND (auth.jwt() ->> 'role') = 'TenantAdmin'
    );

-- Tenant admins can update their business profile
CREATE POLICY "Tenant admins can update business profile"
    ON business_profiles FOR UPDATE
    USING (
        tenant_id = (auth.jwt() ->> 'tenant_id')::uuid 
        AND (auth.jwt() ->> 'role') = 'TenantAdmin'
    )
    WITH CHECK (
        tenant_id = (auth.jwt() ->> 'tenant_id')::uuid 
        AND (auth.jwt() ->> 'role') = 'TenantAdmin'
    );

-- Only tenant admins can delete business profiles (soft delete via status)
CREATE POLICY "Tenant admins can delete business profile"
    ON business_profiles FOR DELETE
    USING (
        tenant_id = (auth.jwt() ->> 'tenant_id')::uuid 
        AND (auth.jwt() ->> 'role') = 'TenantAdmin'
    );

-- Indexes for performance
CREATE INDEX idx_business_profiles_tenant_id ON business_profiles(tenant_id);
CREATE INDEX idx_business_profiles_user_id ON business_profiles(user_id);
CREATE INDEX idx_business_profiles_status ON business_profiles(status);
CREATE INDEX idx_business_profiles_company_name ON business_profiles(company_name);

-- Updated_at trigger
CREATE TRIGGER update_business_profiles_updated_at 
    BEFORE UPDATE ON business_profiles 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Comments for documentation
COMMENT ON TABLE business_profiles IS 'Business profile information for each tenant organization';
COMMENT ON COLUMN business_profiles.tenant_id IS 'Reference to the tenant this business profile belongs to';
COMMENT ON COLUMN business_profiles.user_id IS 'User who created/owns this business profile';
COMMENT ON COLUMN business_profiles.company_name IS 'Official registered company name';
COMMENT ON COLUMN business_profiles.trading_name IS 'Trading name or DBA if different from company name';
COMMENT ON COLUMN business_profiles.company_number IS 'Companies House registration number (UK)';
COMMENT ON COLUMN business_profiles.vat_number IS 'VAT registration number for tax purposes';
COMMENT ON COLUMN business_profiles.professional_body_registration IS 'Registration with professional bodies (e.g., RICS, CIOB)';
COMMENT ON COLUMN business_profiles.brand_color IS 'Primary brand color in hex format for reports and UI';
COMMENT ON COLUMN business_profiles.default_report_template IS 'Default template to use for generated reports';
