/**
 * Secure API Client for Strata Compliance
 * This client communicates with the API layer instead of directly accessing the database
 */

import type {
  AuthSession,
  Client,
  ClientInsert,
  Site,
  Tenant,
  TenantUser,
  User,
} from "../types/index.js";

export interface ApiResponse<T> {
  data?: T;
  error?: string;
}

export interface AuthResponse {
  user?: User;
  session?: AuthSession;
  error?: string;
}

export class ApiClient {
  private baseUrl: string;
  private authToken: string | null = null;

  constructor(baseUrl: string) {
    this.baseUrl = baseUrl.replace(/\/$/, ""); // Remove trailing slash
  }

  // Set authentication token
  setAuthToken(token: string | null) {
    this.authToken = token;
  }

  // Private method to make authenticated requests
  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    const url = `${this.baseUrl}${endpoint}`;

    const headers: Record<string, string> = {
      "Content-Type": "application/json",
      ...((options.headers as Record<string, string>) || {}),
    };

    // Add auth token if available
    if (this.authToken) {
      headers["Authorization"] = `Bearer ${this.authToken}`;
    }

    try {
      const response = await fetch(url, {
        ...options,
        headers,
      });

      const data = await response.json();

      if (!response.ok) {
        return { error: data.error || `HTTP ${response.status}` };
      }

      return { data };
    } catch (error) {
      return {
        error: error instanceof Error ? error.message : "Network error",
      };
    }
  }

  // Authentication methods
  async signIn(email: string, password: string): Promise<AuthResponse> {
    const response = await this.request<AuthResponse>("/auth/signin", {
      method: "POST",
      body: JSON.stringify({ email, password }),
    });

    if (response.data?.session?.access_token) {
      this.setAuthToken(response.data.session.access_token);
    }

    return response.data || { error: response.error };
  }

  async signUp(email: string, password: string): Promise<AuthResponse> {
    const response = await this.request<AuthResponse>("/auth/signup", {
      method: "POST",
      body: JSON.stringify({ email, password }),
    });

    if (response.data?.session?.access_token) {
      this.setAuthToken(response.data.session.access_token);
    }

    return response.data || { error: response.error };
  }

  async signOut(): Promise<void> {
    this.setAuthToken(null);
  }

  // Tenant methods
  async getTenant(): Promise<ApiResponse<Tenant>> {
    return this.request<Tenant>("/api/tenant");
  }

  // Client methods
  async getClients(): Promise<ApiResponse<Client[]>> {
    const response = await this.request<{ clients: Client[] }>("/api/clients");

    if (response.error) {
      return { error: response.error };
    }

    return { data: response.data?.clients || [] };
  }

  async createClient(client: ClientInsert): Promise<ApiResponse<Client>> {
    const response = await this.request<{ client: Client }>("/api/clients", {
      method: "POST",
      body: JSON.stringify(client),
    });

    if (response.error) {
      return { error: response.error };
    }

    return { data: response.data?.client };
  }

  // Site methods
  async getSites(): Promise<ApiResponse<Site[]>> {
    const response = await this.request<{ sites: Site[] }>("/api/sites");

    if (response.error) {
      return { error: response.error };
    }

    return { data: response.data?.sites || [] };
  }

  // User management methods
  async addUserToTenant(
    tenantId: string,
    userId: string,
    role: "TenantAdmin" | "Scheduler" | "Inspector" | "ClientUser",
    firstName?: string,
    lastName?: string
  ): Promise<ApiResponse<TenantUser>> {
    return this.request<TenantUser>("/api/users", {
      method: "POST",
      body: JSON.stringify({
        tenantId,
        userId,
        role,
        firstName,
        lastName,
      }),
    });
  }

  async getUserTenantInfo(): Promise<ApiResponse<TenantUser>> {
    return this.request<TenantUser>("/api/user/tenant-info");
  }

  // Health check
  async healthCheck(): Promise<
    ApiResponse<{ message: string; version: string; timestamp: string }>
  > {
    return this.request("/");
  }
}

// Browser-safe environment variable access
function getEnvVar(name: string): string {
  // Check if we're in a browser environment
  if (typeof window !== "undefined") {
    // In browser, check for Vite env vars on import.meta.env
    if (typeof import.meta !== "undefined" && (import.meta as any).env) {
      return (import.meta as any).env[name] || "";
    }
    // Fallback to empty string in browser if import.meta.env not available
    return "";
  }

  // In Node.js environment, use process.env
  if (typeof process !== "undefined" && process.env) {
    return process.env[name] || "";
  }

  return "";
}

// Default API client instance
export const apiClient = new ApiClient(
  getEnvVar("VITE_API_URL") ||
    getEnvVar("NEXT_PUBLIC_API_URL") ||
    "http://localhost:8787" // Default for local development
);

// Hook-style interface for React components
export const useApiClient = () => {
  return apiClient;
};
