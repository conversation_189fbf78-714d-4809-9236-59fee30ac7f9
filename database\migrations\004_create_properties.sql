-- Create properties table (adapted from tblProperties)
-- Represents individual units/assets within sites that need inspection
CREATE TABLE properties (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    
    -- Multi-tenant
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    
    -- Relationships
    client_id UUID NOT NULL REFERENCES clients(id) ON DELETE CASCADE,
    site_id UUID, -- Will reference sites table (created next)
    
    -- Property identification (from original)
    property_code TEXT,
    notes TEXT,
    
    -- Asset/Property details
    group_name TEXT,
    building_name TEXT,
    building_number TEXT,
    unit TEXT,
    floor TEXT,
    
    -- Images and documents
    image_container_name TEXT, -- For blob storage
    image_blob_name TEXT,
    
    -- QR Code integration
    qr_code_id TEXT,
    qr_code_document_id TEXT,
    
    -- Status and lifecycle
    is_managed BOOLEAN DEFAULT true NOT NULL,
    status TEXT DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'archived')),
    archived_at TIMESTAMPTZ,
    archived_by UUID REFERENCES auth.users(id),
    archive_reason TEXT,
    deleted_at TIMESTAMPTZ -- Soft delete
);

-- RLS policies
ALTER TABLE properties ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view properties in their tenant"
    ON properties FOR SELECT
    USING (tenant_id = (auth.jwt() ->> 'tenant_id')::uuid);

CREATE POLICY "Inspectors can view assigned properties"
    ON properties FOR SELECT
    USING (
        tenant_id = (auth.jwt() ->> 'tenant_id')::uuid 
        AND (auth.jwt() ->> 'role') = 'Inspector'
        -- Additional logic for inspector assignments will be added with inspections table
    );

CREATE POLICY "Schedulers and admins can manage properties"
    ON properties FOR ALL
    USING (
        tenant_id = (auth.jwt() ->> 'tenant_id')::uuid 
        AND (auth.jwt() ->> 'role') IN ('TenantAdmin', 'Scheduler')
    );

-- Indexes
CREATE INDEX idx_properties_tenant_id ON properties(tenant_id);
CREATE INDEX idx_properties_client_id ON properties(client_id);
CREATE INDEX idx_properties_site_id ON properties(site_id);
CREATE INDEX idx_properties_property_code ON properties(property_code);
CREATE INDEX idx_properties_qr_code_id ON properties(qr_code_id);
CREATE INDEX idx_properties_status ON properties(status);
CREATE INDEX idx_properties_is_managed ON properties(is_managed);

-- Updated_at trigger
CREATE TRIGGER update_properties_updated_at 
    BEFORE UPDATE ON properties 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column(); 