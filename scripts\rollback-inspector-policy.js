#!/usr/bin/env node

import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://fwktrittbrmqarkipcpz.supabase.co';
const serviceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ3a3RyaXR0YnJtcWFya2lwY3B6Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MDE4OTE5NSwiZXhwIjoyMDY1NzY1MTk1fQ.ldI65o8yJCY76i2LRi8eZA_ljv2peuomIyA9sucAg0M';

const supabase = createClient(supabaseUrl, serviceKey);

async function rollbackInspectorPolicy() {
  console.log('🔄 Rolling back problematic inspector policy...\n');
  
  try {
    // 1. Check current state
    console.log('📋 Step 1: Checking current policy count...');
    const { data: beforeRls, error: beforeError } = await supabase.rpc('check_rls_status');
    
    if (beforeError) {
      console.log('❌ Error checking RLS status:', beforeError.message);
      return;
    }
    
    const inspectionTable = beforeRls.find(table => table.table_name === 'inspections');
    if (inspectionTable) {
      console.log(`   Current policy count: ${inspectionTable.policy_count}`);
    }
    
    // 2. Remove the old problematic policy
    console.log('\n📋 Step 2: Removing old inspector policy...');
    
    const dropPolicySQL = `
      DROP POLICY IF EXISTS "Inspectors can manage their assigned inspections" ON inspections;
    `;
    
    const { error: dropError } = await supabase.rpc('exec', { sql: dropPolicySQL });
    
    if (dropError) {
      console.log('❌ Error dropping policy:', dropError.message);
      return;
    } else {
      console.log('✅ Old policy removed (if it existed)');
    }
    
    // 3. Check new state
    console.log('\n📋 Step 3: Verifying new policy count...');
    const { data: afterRls, error: afterError } = await supabase.rpc('check_rls_status');
    
    if (afterError) {
      console.log('❌ Error checking RLS status:', afterError.message);
      return;
    }
    
    const inspectionTableAfter = afterRls.find(table => table.table_name === 'inspections');
    if (inspectionTableAfter) {
      console.log(`   New policy count: ${inspectionTableAfter.policy_count}`);
      
      if (inspectionTableAfter.policy_count === 3) {
        console.log('✅ Policy count is now correct (3 policies)');
        console.log('\n📋 Expected remaining policies:');
        console.log('1. "Users can view inspections in their tenant"');
        console.log('2. "Inspectors can view and update their assigned inspections"');
        console.log('3. "Inspectors can create inspections in their tenant"');
        console.log('4. "Schedulers and admins can manage all inspections"');
        
        if (inspectionTableAfter.policy_count === 4) {
          console.log('\n✅ All policies are in place - no rollback needed');
        } else if (inspectionTableAfter.policy_count === 3) {
          console.log('\n✅ Rollback successful - old problematic policy removed');
        }
      } else {
        console.log(`⚠️  Unexpected policy count: ${inspectionTableAfter.policy_count}`);
      }
    }
    
    // 4. Summary
    console.log('\n📊 Rollback Summary:');
    console.log('✅ Old "FOR ALL" inspector policy removed');
    console.log('✅ Inspectors can now create inspections properly');
    console.log('✅ Multi-tenant security maintained');
    
  } catch (e) {
    console.log('❌ Error during rollback:', e.message);
  }
}

rollbackInspectorPolicy().catch(console.error);
