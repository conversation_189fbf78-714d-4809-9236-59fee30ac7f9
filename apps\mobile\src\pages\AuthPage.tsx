import {
  IonButton,
  IonContent,
  IonInput,
  IonPage,
  IonSegment,
  IonSegmentButton,
  IonSpinner,
  IonText,
} from '@ionic/react';
import type { AuthError, User } from '@strata/core';
import React, { useState } from 'react';
import { Redirect } from 'react-router-dom';
import { debugAuth } from '../lib/logging';

interface AuthPageProps {
  user: User | null;
  loading: boolean;
  error: AuthError | null;
  signInWithEmail: (email: string, password: string) => Promise<AuthError | null>;
  signInWithMagicLink: (email: string) => Promise<AuthError | null>;
  signUp: (email: string, password: string) => Promise<AuthError | null>;
}

const AuthPage: React.FC<AuthPageProps> = ({
  user,
  loading,
  error,
  signInWithEmail,
  signInWithMagicLink,
  signUp,
}) => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [authMode, setAuthMode] = useState<'magic' | 'password'>('magic');
  const [isSignUp, setIsSignUp] = useState(false);
  const [magicLinkSent, setMagicLinkSent] = useState(false);

  const handleMagicLinkSignIn = async () => {
    debugAuth('Sending magic link', { email });
    const error = await signInWithMagicLink(email);
    debugAuth('Magic link result', { error: error?.message, hasError: !!error });
    if (!error) {
      debugAuth('Setting magicLinkSent to true');
      setMagicLinkSent(true);
    } else {
      debugAuth('Magic link failed', { error: error?.message });
    }
  };

  const handleEmailChange = (newEmail: string) => {
    setEmail(newEmail);
    // Don't reset magicLinkSent on email changes - let the user see the success message
    // until they switch auth modes or send another magic link
  };

  const handleAuthModeChange = (newMode: 'magic' | 'password') => {
    setAuthMode(newMode);
    setMagicLinkSent(false);
  };

  debugAuth('Auth page render', {
    user: user?.email || 'null',
    loading,
    error: error?.message,
    magicLinkSent
  });

  // If user is authenticated, redirect to dashboard
  if (user) {
    debugAuth('User authenticated, redirecting to dashboard');
    return <Redirect to="/dashboard" />;
  }

  if (loading) {
    debugAuth('Showing auth loading spinner');
    return (
      <IonPage>
        <IonContent className="ion-padding">
          <IonSpinner />
          <p>Loading...</p>
        </IonContent>
      </IonPage>
    );
  }

  debugAuth('Showing auth form');
  return (
    <IonPage>
      <IonContent className="ion-padding">
        <h1>Strata Compliance</h1>
        <p>{isSignUp ? 'Create your account' : 'Please sign in to continue'}</p>

        {/* Show different options based on sign up mode */}
        {!isSignUp ? (
          <>
            {/* Sign In Mode */}
            <IonSegment value={authMode} onIonChange={e => handleAuthModeChange(e.detail.value as 'magic' | 'password')}>
              <IonSegmentButton value="magic">Email Link</IonSegmentButton>
              <IonSegmentButton value="password">Password</IonSegmentButton>
            </IonSegment>

            <IonInput
              label="Email"
              labelPlacement="floating"
              type="email"
              value={email}
              onIonInput={e => handleEmailChange(e.detail.value!)}
              required
            />

            {/* Password field - only show for password mode */}
            {authMode === 'password' && (
              <IonInput
                label="Password"
                labelPlacement="floating"
                type="password"
                value={password}
                onIonInput={e => setPassword(e.detail.value!)}
                required
              />
            )}

            {error && <IonText color="danger"><p>{error.message}</p></IonText>}

            {/* Success message for magic link */}
            {magicLinkSent && (
              <IonText color="success">
                <p>✅ Check your email! We've sent you a sign-in link.</p>
              </IonText>
            )}

            {/* Sign In Action Button */}
            {authMode === 'magic' ? (
              <IonButton
                expand="block"
                onClick={handleMagicLinkSignIn}
                disabled={!email || loading}
              >
                Continue with Email
              </IonButton>
            ) : (
              <IonButton
                expand="block"
                onClick={() => signInWithEmail(email, password)}
                disabled={!email || !password || loading}
              >
                Sign In
              </IonButton>
            )}

            <p>
              Don't have an account?{' '}
              <IonButton fill="clear" onClick={() => setIsSignUp(true)}>
                Sign Up
              </IonButton>
            </p>
          </>
        ) : (
          <>
            {/* Sign Up Mode */}
            <IonInput
              label="Email"
              labelPlacement="floating"
              type="email"
              value={email}
              onIonInput={e => setEmail(e.detail.value!)}
              required
            />

            <IonInput
              label="Password"
              labelPlacement="floating"
              type="password"
              value={password}
              onIonInput={e => setPassword(e.detail.value!)}
              required
            />

            {error && <IonText color="danger"><p>{error.message}</p></IonText>}

            <IonButton
              expand="block"
              onClick={() => signUp(email, password)}
              disabled={!email || !password || loading}
            >
              Create Account
            </IonButton>

            <p>
              Already have an account?{' '}
              <IonButton fill="clear" onClick={() => setIsSignUp(false)}>
                Sign In
              </IonButton>
            </p>
          </>
        )}
      </IonContent>
    </IonPage>
  );
};

export default AuthPage;
