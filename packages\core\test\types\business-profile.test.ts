import { describe, expect, it } from "vitest";
import type {
  BusinessProfile,
  BusinessProfileInsert,
  BusinessProfileUpdate,
  CreateBusinessProfileRequest,
  EntityStatus,
  UpdateBusinessProfileRequest,
} from "../../src/types";

describe("BusinessProfile Types", () => {
  describe("BusinessProfile interface", () => {
    it("should have all required fields", () => {
      const businessProfile: BusinessProfile = {
        id: "123e4567-e89b-12d3-a456-426614174000",
        created_at: "2024-01-01T00:00:00Z",
        updated_at: "2024-01-01T00:00:00Z",
        tenant_id: "123e4567-e89b-12d3-a456-426614174001",
        user_id: "123e4567-e89b-12d3-a456-426614174002",
        company_name: "Strata Compliance Ltd",
        country: "United Kingdom",
        default_report_template: "standard",
        default_currency: "GBP",
        default_timezone: "Europe/London",
        status: "active",
      };

      expect(businessProfile.id).toBeDefined();
      expect(businessProfile.company_name).toBeDefined();
      expect(businessProfile.tenant_id).toBeDefined();
      expect(businessProfile.user_id).toBeDefined();
      expect(businessProfile.status).toBeDefined();
    });

    it("should allow optional fields to be undefined", () => {
      const businessProfile: BusinessProfile = {
        id: "123e4567-e89b-12d3-a456-426614174000",
        created_at: "2024-01-01T00:00:00Z",
        updated_at: "2024-01-01T00:00:00Z",
        tenant_id: "123e4567-e89b-12d3-a456-426614174001",
        user_id: "123e4567-e89b-12d3-a456-426614174002",
        company_name: "Strata Compliance Ltd",
        country: "United Kingdom",
        default_report_template: "standard",
        default_currency: "GBP",
        default_timezone: "Europe/London",
        status: "active",
        // Optional fields not provided
        trading_name: undefined,
        email: undefined,
        phone: undefined,
        logo_url: undefined,
      };

      expect(businessProfile).toBeDefined();
    });

    it("should enforce valid status values", () => {
      const validStatuses: EntityStatus[] = ["active", "inactive", "archived"];

      validStatuses.forEach((status) => {
        const businessProfile: BusinessProfile = {
          id: "123e4567-e89b-12d3-a456-426614174000",
          created_at: "2024-01-01T00:00:00Z",
          updated_at: "2024-01-01T00:00:00Z",
          tenant_id: "123e4567-e89b-12d3-a456-426614174001",
          user_id: "123e4567-e89b-12d3-a456-426614174002",
          company_name: "Strata Compliance Ltd",
          country: "United Kingdom",
          default_report_template: "standard",
          default_currency: "GBP",
          default_timezone: "Europe/London",
          status,
        };

        expect(businessProfile.status).toBe(status);
      });
    });
  });

  describe("BusinessProfileInsert type", () => {
    it("should exclude auto-generated fields", () => {
      const insert: BusinessProfileInsert = {
        tenant_id: "123e4567-e89b-12d3-a456-426614174001",
        user_id: "123e4567-e89b-12d3-a456-426614174002",
        company_name: "Strata Compliance Ltd",
        country: "United Kingdom",
        default_report_template: "standard",
        default_currency: "GBP",
        default_timezone: "Europe/London",
        status: "active",
      };

      // Verify the insert object doesn't have auto-generated fields
      expect(insert).not.toHaveProperty("id");
      expect(insert).not.toHaveProperty("created_at");
      expect(insert).not.toHaveProperty("updated_at");

      // Verify required fields are present
      expect(insert.tenant_id).toBeDefined();
      expect(insert.user_id).toBeDefined();
      expect(insert.company_name).toBeDefined();
    });

    it("should require mandatory fields", () => {
      const insert: BusinessProfileInsert = {
        tenant_id: "123e4567-e89b-12d3-a456-426614174001",
        user_id: "123e4567-e89b-12d3-a456-426614174002",
        company_name: "Strata Compliance Ltd",
        country: "United Kingdom",
        default_report_template: "standard",
        default_currency: "GBP",
        default_timezone: "Europe/London",
        status: "active",
      };

      expect(insert.tenant_id).toBeDefined();
      expect(insert.user_id).toBeDefined();
      expect(insert.company_name).toBeDefined();
    });
  });

  describe("BusinessProfileUpdate type", () => {
    it("should make all fields optional", () => {
      const update: BusinessProfileUpdate = {
        company_name: "Updated Company Name",
      };

      expect(update).toBeDefined();
      expect(update.company_name).toBe("Updated Company Name");
    });

    it("should allow partial updates", () => {
      const update: BusinessProfileUpdate = {
        email: "<EMAIL>",
        phone: "+44 20 1234 5678",
        brand_color: "#FF0000",
      };

      expect(update.email).toBe("<EMAIL>");
      expect(update.phone).toBe("+44 20 1234 5678");
      expect(update.brand_color).toBe("#FF0000");
    });
  });

  describe("CreateBusinessProfileRequest interface", () => {
    it("should only require company_name", () => {
      const request: CreateBusinessProfileRequest = {
        company_name: "Strata Compliance Ltd",
      };

      expect(request.company_name).toBe("Strata Compliance Ltd");
    });

    it("should accept all optional business information", () => {
      const request: CreateBusinessProfileRequest = {
        company_name: "Strata Compliance Ltd",
        trading_name: "Strata",
        company_number: "12345678",
        vat_number: "GB123456789",
        email: "<EMAIL>",
        phone: "+44 20 1234 5678",
        mobile: "+44 7700 900123",
        website: "https://strata.com",
        address_line_1: "123 Business Street",
        address_line_2: "Suite 100",
        city: "London",
        state_province: "England",
        postal_code: "SW1A 1AA",
        country: "United Kingdom",
        industry: "Compliance Services",
        business_type: "limited_company",
        professional_body_registration: "RICS123456",
        insurance_policy_number: "INS123456",
        insurance_expiry_date: "2024-12-31",
        logo_url: "https://strata.com/logo.png",
        brand_color: "#0066CC",
        report_footer_text: "Strata Compliance Ltd - Professional Services",
        default_report_template: "professional",
        default_currency: "GBP",
        default_timezone: "Europe/London",
        financial_year_end: "2024-03-31",
      };

      expect(request).toBeDefined();
      expect(request.company_name).toBe("Strata Compliance Ltd");
      expect(request.vat_number).toBe("GB123456789");
      expect(request.brand_color).toBe("#0066CC");
    });
  });

  describe("UpdateBusinessProfileRequest interface", () => {
    it("should make all fields optional", () => {
      const request: UpdateBusinessProfileRequest = {};
      expect(request).toBeDefined();
    });

    it("should allow selective updates", () => {
      const request: UpdateBusinessProfileRequest = {
        email: "<EMAIL>",
        brand_color: "#FF6600",
        status: "inactive",
      };

      expect(request.email).toBe("<EMAIL>");
      expect(request.brand_color).toBe("#FF6600");
      expect(request.status).toBe("inactive");
    });

    it("should include status field for archiving", () => {
      const request: UpdateBusinessProfileRequest = {
        status: "archived",
      };

      expect(request.status).toBe("archived");
    });
  });

  describe("Type compatibility", () => {
    it("should allow CreateBusinessProfileRequest to be used for insert operations", () => {
      const createRequest: CreateBusinessProfileRequest = {
        company_name: "Test Company",
        email: "<EMAIL>",
      };

      // This should be compatible with insert operations
      const insertData: Partial<BusinessProfileInsert> = {
        ...createRequest,
        tenant_id: "123e4567-e89b-12d3-a456-426614174001",
        user_id: "123e4567-e89b-12d3-a456-426614174002",
      };

      expect(insertData.company_name).toBe("Test Company");
      expect(insertData.email).toBe("<EMAIL>");
      expect(insertData.tenant_id).toBeDefined();
      expect(insertData.user_id).toBeDefined();
    });

    it("should allow UpdateBusinessProfileRequest to be used for update operations", () => {
      const updateRequest: UpdateBusinessProfileRequest = {
        company_name: "Updated Company",
        phone: "+44 20 9876 5432",
      };

      // This should be compatible with update operations
      const updateData: BusinessProfileUpdate = updateRequest;

      expect(updateData.company_name).toBe("Updated Company");
      expect(updateData.phone).toBe("+44 20 9876 5432");
    });
  });
});
