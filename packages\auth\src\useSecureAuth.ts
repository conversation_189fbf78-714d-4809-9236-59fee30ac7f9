/**
 * Secure Authentication Hook
 * Uses API client instead of direct database access
 */

import type { AuthError, AuthSession, User } from "@strata/core";
import { apiClient } from "@strata/core";
import { useCallback, useEffect, useState } from "react";

interface TenantInfo {
  tenantId: string | null;
  role: string | null;
  userProfile: {
    first_name?: string;
    last_name?: string;
    position?: string;
  };
}

interface UseSecureAuthReturn {
  user: User | null;
  session: AuthSession | null;
  loading: boolean;
  error: AuthError | null;
  tenantInfo: TenantInfo | null;
  signInWithEmail: (
    email: string,
    password: string
  ) => Promise<AuthError | null>;
  signUp: (email: string, password: string) => Promise<AuthError | null>;
  signOut: () => Promise<void>;
  hasRole: (role: string) => boolean;
  isTenantAdmin: () => boolean;
  refreshTenantInfo: () => Promise<void>;
}

export const useSecureAuth = (): UseSecureAuthReturn => {
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<AuthSession | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<AuthError | null>(null);
  const [tenantInfo, setTenantInfo] = useState<TenantInfo | null>(null);

  // Load session from localStorage on mount
  useEffect(() => {
    const loadSession = () => {
      try {
        const storedSession = localStorage.getItem("strata_session");
        const storedUser = localStorage.getItem("strata_user");

        if (storedSession && storedUser) {
          const sessionData = JSON.parse(storedSession);
          const userData = JSON.parse(storedUser);

          // Check if session is still valid (not expired)
          if (
            sessionData.expires_at &&
            new Date(sessionData.expires_at) > new Date()
          ) {
            setSession(sessionData);
            setUser(userData);
            apiClient.setAuthToken(sessionData.access_token);

            // Load tenant info
            loadTenantInfo();
          } else {
            // Session expired, clear storage
            clearSession();
          }
        }
      } catch (e) {
        console.error("Error loading session:", e);
        clearSession();
      } finally {
        setLoading(false);
      }
    };

    loadSession();
  }, []);

  // Clear session data
  const clearSession = useCallback(() => {
    localStorage.removeItem("strata_session");
    localStorage.removeItem("strata_user");
    setUser(null);
    setSession(null);
    setTenantInfo(null);
    apiClient.setAuthToken(null);
  }, []);

  // Load tenant info from API
  const loadTenantInfo = useCallback(async () => {
    try {
      const response = await apiClient.getUserTenantInfo();

      if (response.data) {
        setTenantInfo({
          tenantId: response.data.tenant_id,
          role: response.data.role,
          userProfile: {
            first_name: response.data.first_name,
            last_name: response.data.last_name,
            position: response.data.position,
          },
        });
      } else {
        setTenantInfo({
          tenantId: null,
          role: null,
          userProfile: {},
        });
      }
    } catch (e) {
      console.error("Error loading tenant info:", e);
      setTenantInfo(null);
    }
  }, []);

  // Sign in with email and password
  const signInWithEmail = useCallback(
    async (email: string, password: string): Promise<AuthError | null> => {
      setLoading(true);
      setError(null);

      try {
        const response = await apiClient.signIn(email, password);

        if (response.error) {
          const authError = { message: response.error } as AuthError;
          setError(authError);
          return authError;
        }

        if (response.user && response.session) {
          setUser(response.user);
          setSession(response.session);

          // Store session in localStorage
          localStorage.setItem(
            "strata_session",
            JSON.stringify(response.session)
          );
          localStorage.setItem("strata_user", JSON.stringify(response.user));

          // Load tenant info
          await loadTenantInfo();
        }

        return null;
      } catch (e) {
        const authError = {
          message: e instanceof Error ? e.message : "Sign in failed",
        } as AuthError;
        setError(authError);
        return authError;
      } finally {
        setLoading(false);
      }
    },
    [loadTenantInfo]
  );

  // Sign up with email and password
  const signUp = useCallback(
    async (email: string, password: string): Promise<AuthError | null> => {
      setLoading(true);
      setError(null);

      try {
        const response = await apiClient.signUp(email, password);

        if (response.error) {
          const authError = { message: response.error } as AuthError;
          setError(authError);
          return authError;
        }

        if (response.user && response.session) {
          setUser(response.user);
          setSession(response.session);

          // Store session in localStorage
          localStorage.setItem(
            "strata_session",
            JSON.stringify(response.session)
          );
          localStorage.setItem("strata_user", JSON.stringify(response.user));

          // Load tenant info (may be null for new users)
          await loadTenantInfo();
        }

        return null;
      } catch (e) {
        const authError = {
          message: e instanceof Error ? e.message : "Sign up failed",
        } as AuthError;
        setError(authError);
        return authError;
      } finally {
        setLoading(false);
      }
    },
    [loadTenantInfo]
  );

  // Sign out
  const signOut = useCallback(async (): Promise<void> => {
    setLoading(true);

    try {
      await apiClient.signOut();
    } catch (e) {
      console.error("Error during sign out:", e);
    } finally {
      clearSession();
      setLoading(false);
    }
  }, [clearSession]);

  // Check if user has specific role
  const hasRole = useCallback(
    (role: string): boolean => {
      return tenantInfo?.role === role;
    },
    [tenantInfo]
  );

  // Check if user is tenant admin
  const isTenantAdmin = useCallback((): boolean => {
    return hasRole("TenantAdmin");
  }, [hasRole]);

  // Refresh tenant info
  const refreshTenantInfo = useCallback(async (): Promise<void> => {
    await loadTenantInfo();
  }, [loadTenantInfo]);

  return {
    user,
    session,
    loading,
    error,
    tenantInfo,
    signInWithEmail,
    signUp,
    signOut,
    hasRole,
    isTenantAdmin,
    refreshTenantInfo,
  };
};
