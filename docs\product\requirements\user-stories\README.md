# User Stories – Guidance & Template

This folder contains user stories for Strata Compliance, organized by feature or workflow. Agents and contributors should use the following format for each user story:

## User Story Template

- **ID:** (e.g., 001-onboarding-single-user)
- **Title:** Short, descriptive title
- **As a**: (user type)
- **I want to**: (goal/action)
- **So that**: (value/benefit)
- **Acceptance Criteria:**
  - [ ] List clear, testable criteria
  - [ ] ...
- **Dependencies:** (other stories, flows, or features required)
- **Notes:** (optional, for clarifications or links)

## Example

- **ID:** 001-onboarding-single-user
- **Title:** Onboard as a single user
- **As a**: new solo surveyor
- **I want to**: sign up and set up my business profile in the app
- **So that**: I can start creating and managing surveys
- **Acceptance Criteria:**
  - [ ] User can register with email and password
  - [ ] User can upload company logo and contact details
  - [ ] User is prompted to create their first job after onboarding
- **Dependencies:** None
- **Notes:** See onboarding flow in ../flows/onboarding/single-user.md

---

All user stories should be written in this format for clarity and consistency.
