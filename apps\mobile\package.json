{"name": "@strata/mobile", "version": "0.0.1", "private": true, "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint src/", "preview": "vite preview", "test": "vitest run"}, "dependencies": {"@capacitor/core": "^5.7.0", "@capacitor/ios": "^5.7.0", "@capacitor/splash-screen": "^5.0.7", "@ionic/react": "^7.7.3", "@ionic/react-router": "^7.7.3", "@strata/auth": "workspace:*", "@strata/core": "workspace:*", "@strata/ui": "workspace:*", "ionicons": "^7.2.2", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^5.3.4"}, "devDependencies": {"@capacitor/cli": "^5.7.0", "@types/react": "^18.2.55", "@types/react-dom": "^18.2.19", "@types/react-router-dom": "^5.3.3", "@vitejs/plugin-react": "^4.2.1", "eslint": "^8.56.0", "typescript": "^5.3.3", "vite": "^5.1.3", "vitest": "^1.2.2"}}