-- Create tenants table (top-level organizations)
CREATE TABLE tenants (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    
    -- Organization details
    name TEXT NOT NULL,
    slug TEXT UNIQUE NOT NULL, -- URL-safe identifier
    logo_url TEXT,
    
    -- Subscription & billing
    subscription_tier TEXT DEFAULT 'free' CHECK (subscription_tier IN ('free', 'pro', 'enterprise')),
    max_users INTEGER DEFAULT 5,
    max_clients INTEGER DEFAULT 10,
    
    -- Settings
    timezone TEXT DEFAULT 'UTC',
    date_format TEXT DEFAULT 'DD/MM/YYYY',
    currency TEXT DEFAULT 'GBP',
    
    -- Status
    status TEXT DEFAULT 'active' CHECK (status IN ('active', 'suspended', 'archived')),
    archived_at TIMESTAMPTZ,
    archived_by UUID
);

-- RLS policies
ALTER TABLE tenants ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own tenant"
    ON tenants FOR SELECT
    USING (id = (auth.jwt() ->> 'tenant_id')::uuid);

CREATE POLICY "Only tenant admins can update tenant details"
    ON tenants FOR UPDATE
    USING (
        id = (auth.jwt() ->> 'tenant_id')::uuid 
        AND (auth.jwt() ->> 'role') = 'TenantAdmin'
    );

-- Indexes
CREATE INDEX idx_tenants_slug ON tenants(slug);
CREATE INDEX idx_tenants_status ON tenants(status);

-- Trigger for updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_tenants_updated_at 
    BEFORE UPDATE ON tenants 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column(); 