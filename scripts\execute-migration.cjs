#!/usr/bin/env node

/**
 * Migration execution helper for Supabase
 * Displays SQL and provides verification
 */

const fs = require('fs');
const path = require('path');

// Load environment variables manually
const envPath = path.join(__dirname, '..', '.env.local');
const envContent = fs.readFileSync(envPath, 'utf8');
const envLines = envContent.split('\n');

// Parse environment variables
const env = {};
envLines.forEach(line => {
  const [key, ...valueParts] = line.split('=');
  if (key && valueParts.length > 0) {
    env[key.trim()] = valueParts.join('=').trim();
  }
});

const supabaseUrl = env.NEXT_PUBLIC_SUPABASE_URL;

async function displayMigration() {
  console.log('🚀 Business Profiles Migration Setup');
  console.log('=====================================\n');
  
  // Read the migration file
  const migrationPath = path.join(__dirname, '..', 'database', 'migrations', '006_create_business_profiles.sql');
  const sql = fs.readFileSync(migrationPath, 'utf8');
  
  console.log('📋 MANUAL EXECUTION REQUIRED:');
  console.log('1. Open Supabase Dashboard SQL Editor');
  console.log(`2. Go to: ${supabaseUrl.replace('https://', 'https://supabase.com/dashboard/project/')}/sql`);
  console.log('3. Copy and paste the following SQL:');
  console.log('\n' + '='.repeat(80));
  console.log(sql);
  console.log('='.repeat(80) + '\n');
  console.log('4. Click "Run" to execute the migration');
  console.log('5. Verify the table was created successfully');
  console.log('\n✅ After execution, run: node scripts/verify-migration.js');
}

displayMigration().catch(console.error);
