#!/usr/bin/env node

import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://fwktrittbrmqarkipcpz.supabase.co';
const serviceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ3a3RyaXR0YnJtcWFya2lwY3B6Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MDE4OTE5NSwiZXhwIjoyMDY1NzY1MTk1fQ.ldI65o8yJCY76i2LRi8eZA_ljv2peuomIyA9sucAg0M';

const supabase = createClient(supabaseUrl, serviceKey);

async function configureRLS() {
  console.log('🔒 Configuring Row Level Security');
  console.log('==================================\n');
  
  // Enable RLS on all tables
  const tables = ['tenants', 'tenant_users', 'clients', 'properties', 'sites', 'inspections'];
  
  console.log('📋 Enabling RLS on tables...');
  for (const table of tables) {
    try {
      // Enable RLS
      const { error } = await supabase.rpc('enable_rls', { table_name: table });
      
      if (error) {
        console.log(`   ⚠️  Could not enable RLS via RPC for '${table}': ${error.message}`);
        console.log(`   💡 Please run manually: ALTER TABLE ${table} ENABLE ROW LEVEL SECURITY;`);
      } else {
        console.log(`   ✅ RLS enabled for '${table}'`);
      }
    } catch (e) {
      console.log(`   ⚠️  Error enabling RLS for '${table}': ${e.message}`);
    }
  }
  
  // Test RLS enforcement
  console.log('\n🧪 Testing RLS enforcement...');
  
  const anonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ3a3RyaXR0YnJtcWFya2lwY3B6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAxODkxOTUsImV4cCI6MjA2NTc2NTE5NX0.cbfaZXV4NwBwRXVSRF0xSzWVUTmfXlwZa4ptuOF0se0';
  const anonClient = createClient(supabaseUrl, anonKey);
  
  for (const table of tables) {
    try {
      const { data, error } = await anonClient.from(table).select('*').limit(1);
      
      if (error) {
        console.log(`   ✅ '${table}': RLS working (${error.message})`);
      } else {
        console.log(`   ⚠️  '${table}': RLS not enforced (returned ${data?.length || 0} rows)`);
      }
    } catch (e) {
      console.log(`   ✅ '${table}': RLS working (access denied)`);
    }
  }
  
  console.log('\n📋 Manual RLS Configuration Required');
  console.log('=====================================');
  console.log('If RLS is not working, please run these commands in Supabase SQL Editor:');
  console.log('');
  
  for (const table of tables) {
    console.log(`-- Enable RLS for ${table}`);
    console.log(`ALTER TABLE ${table} ENABLE ROW LEVEL SECURITY;`);
    console.log('');
  }
  
  console.log('-- Create basic policies (example for tenants table)');
  console.log(`CREATE POLICY "Users can view their own tenant" ON tenants FOR SELECT`);
  console.log(`    USING (id = (auth.jwt() ->> 'tenant_id')::uuid);`);
  console.log('');
  console.log('See database/migrations/ files for complete policy definitions.');
}

configureRLS().catch(console.error);
