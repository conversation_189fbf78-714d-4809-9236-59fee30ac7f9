import React from 'react';
import { createRoot } from 'react-dom/client';
import { BrowserRouter as Router, Routes, Route, Link } from 'react-router-dom';
import AppBar from '@mui/material/AppBar';
import Box from '@mui/material/Box';
import Toolbar from '@mui/material/Toolbar';
import Typography from '@mui/material/Typography';
import Drawer from '@mui/material/Drawer';
import List from '@mui/material/List';
import ListItem from '@mui/material/ListItem';
import ListItemButton from '@mui/material/ListItemButton';
import ListItemText from '@mui/material/ListItemText';
import { useSupabaseAuth } from '@strata/auth';
import { useState } from 'react';
import Button from '@mui/material/Button';
import TextField from '@mui/material/TextField';
import Alert from '@mui/material/Alert';
import ToggleButton from '@mui/material/ToggleButton';
import ToggleButtonGroup from '@mui/material/ToggleButtonGroup';

const drawerWidth = 200;

const Splash = () => <Box p={2}><Typography variant="h4">Splash Screen</Typography></Box>;
const Auth = () => {
  const { user, loading, error, signInWithEmail, signInWithMagicLink, signOut } = useSupabaseAuth();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [mode, setMode] = useState<'password' | 'magic'>('password');

  if (loading) return <Box p={2}><Typography>Loading...</Typography></Box>;
  if (user) return (
    <Box p={2}>
      <Typography variant="h6">Welcome, {user.email}</Typography>
      <Button variant="contained" color="primary" onClick={() => signOut()} sx={{ mt: 2 }}>Sign Out</Button>
    </Box>
  );

  return (
    <Box p={2}>
      <ToggleButtonGroup
        value={mode}
        exclusive
        onChange={(_e, val) => val && setMode(val)}
        sx={{ mb: 2 }}
      >
        <ToggleButton value="password">Password</ToggleButton>
        <ToggleButton value="magic">Magic Link</ToggleButton>
      </ToggleButtonGroup>
      <TextField
        label="Email"
        type="email"
        value={email}
        onChange={e => setEmail(e.target.value)}
        fullWidth
        margin="normal"
        required
      />
      {mode === 'password' && (
        <TextField
          label="Password"
          type="password"
          value={password}
          onChange={e => setPassword(e.target.value)}
          fullWidth
          margin="normal"
          required
        />
      )}
      {error && <Alert severity="error">{error.message}</Alert>}
      {mode === 'password' ? (
        <Button variant="contained" color="primary" onClick={() => signInWithEmail(email, password)} disabled={!email || !password || loading} fullWidth sx={{ mt: 2 }}>
          Sign In
        </Button>
      ) : (
        <Button variant="contained" color="primary" onClick={() => signInWithMagicLink(email)} disabled={!email || loading} fullWidth sx={{ mt: 2 }}>
          Send Magic Link
        </Button>
      )}
    </Box>
  );
};
const Dashboard = () => <Box p={2}><Typography variant="h4">Surveyor Dashboard</Typography></Box>;

const menuItems = [
  { text: 'Splash', path: '/splash' },
  { text: 'Auth', path: '/auth' },
  { text: 'Dashboard', path: '/dashboard' },
];

const App = () => (
  <Router>
    <Box sx={{ display: 'flex' }}>
      <AppBar position="fixed" sx={{ zIndex: (theme) => theme.zIndex.drawer + 1 }}>
        <Toolbar>
          <Typography variant="h6" noWrap component="div">
            Strata Desktop
          </Typography>
        </Toolbar>
      </AppBar>
      <Drawer
        variant="permanent"
        sx={{
          width: drawerWidth,
          flexShrink: 0,
          [`& .MuiDrawer-paper`]: { width: drawerWidth, boxSizing: 'border-box' },
        }}
      >
        <Toolbar />
        <Box sx={{ overflow: 'auto' }}>
          <List>
            {menuItems.map((item) => (
              <ListItem key={item.text} disablePadding>
                <ListItemButton component={Link} to={item.path}>
                  <ListItemText primary={item.text} />
                </ListItemButton>
              </ListItem>
            ))}
          </List>
        </Box>
      </Drawer>
      <Box component="main" sx={{ flexGrow: 1, p: 3 }}>
        <Toolbar />
        <Routes>
          <Route path="/splash" element={<Splash />} />
          <Route path="/auth" element={<Auth />} />
          <Route path="/dashboard" element={<Dashboard />} />
          <Route path="*" element={<Splash />} />
        </Routes>
      </Box>
    </Box>
  </Router>
);

const root = createRoot(document.getElementById('root')!);
root.render(<App />); 