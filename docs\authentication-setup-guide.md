# Authentication Setup Guide

Complete guide for setting up JWT claims and role-based access control in Strata Compliance.

## 🎯 Overview

This guide configures:

- **JWT Claims**: Custom claims for `tenant_id`, `role`, and user profile
- **Row Level Security (RLS)**: Multi-tenant data isolation
- **Role-Based Access Control**: <PERSON><PERSON><PERSON><PERSON><PERSON>, Scheduler, Inspector, ClientUser roles
- **Authentication Flow**: Sign up, sign in, and tenant association

## 📋 Current Status

- ✅ Database schema deployed (tables exist)
- ✅ Supabase project configured
- ✅ Environment variables set
- ❌ RLS policies need to be configured
- ❌ JWT claims hook needs manual setup
- ✅ User management functions ready in DatabaseService

## ⚠️ Important Constraints

**Auth Schema Limitation**: We cannot directly modify the `auth` schema due to permissions. The JWT claims hook must be configured manually through the Supabase Dashboard.

## 🔧 Step 1: Configure RLS Policies

### 1.1 Run RLS Configuration

Go to [Supabase SQL Editor](https://fwktrittbrmqarkipcpz.supabase.co) and run:

**Copy and paste the entire contents of `database/rls-only-setup.sql`**

This will:

- Enable RLS on all tables
- Create multi-tenant isolation policies
- Add helper functions for user management
- Set up role-based access control

## 🔑 Step 2: Configure JWT Claims Hook (Manual)

**⚠️ IMPORTANT: Authentication Hooks may not be available in the standard Supabase Dashboard UI**

### 2.1 Check Hook Availability

1. Go to your [Supabase Dashboard](https://fwktrittbrmqarkipcpz.supabase.co)
2. Navigate to **Authentication** section
3. Look for a **"Hooks"** subsection or tab

**If you don't see "Hooks" in the Authentication section:**

- Authentication Hooks may not be available in your Supabase plan/tier
- The feature may be limited to self-hosted or enterprise versions
- The UI may have changed since documentation was written

### 2.2 Alternative Approach: Client-Side JWT Decoding

Since the Authentication Hooks UI may not be available, we've implemented client-side JWT decoding in the `useSupabaseAuth` hook:

```typescript
// This is already implemented in packages/auth/src/useSupabaseAuth.ts
const getTenantInfo = () => {
  if (!session?.access_token) return null;

  try {
    // Decode JWT to get any existing custom claims
    const payload = JSON.parse(atob(session.access_token.split(".")[1]));
    return {
      tenantId: payload.tenant_id || null,
      role: payload.role || null,
      userProfile: payload.user_profile || {},
    };
  } catch (e) {
    return null;
  }
};
```

### 2.3 Manual User-Tenant Association

Without JWT hooks, you'll need to:

1. **Store tenant association** in the `tenant_users` table
2. **Query tenant info** after authentication using `DatabaseService.getUserTenantInfo()`
3. **Use client-side state** to manage tenant context in your app

This approach is actually more reliable and doesn't depend on Supabase's hook availability.

## 🔒 Step 3: Verify RLS Configuration

Run the test script to verify RLS is working:

```bash
cd scripts
node test-rls-simple.js
```

Expected output:

- ✅ All tables should show "RLS working"
- ❌ If any show "RLS not enforced", re-run the SQL script

## 👤 Step 4: Test User Flow

### 3.1 Create Test User

1. **Sign up** through your app (mobile or desktop)
2. Note the user ID from the response or check `auth.users` table

### 4.2 Associate User with Tenant

**Option A: Using DatabaseService (Recommended)**

```typescript
import { DatabaseService } from "@strata/core";
import { supabase } from "@strata/auth";

const db = new DatabaseService(supabase);

// Add user to tenant after signup
await db.addUserToTenant(
  "da8d1551-8af3-4d44-a904-68e7bb2997a3", // Demo tenant ID
  user.id, // User ID from signup
  "TenantAdmin", // Role
  "Test", // First name
  "User" // Last name
);
```

**Option B: Direct SQL (Alternative)**

```sql
-- Replace USER_ID with actual user ID from auth.users
INSERT INTO tenant_users (tenant_id, user_id, role, first_name, last_name, status)
VALUES (
  'da8d1551-8af3-4d44-a904-68e7bb2997a3',  -- Demo tenant ID (created by setup)
  'USER_ID_FROM_AUTH_USERS',               -- Replace with actual user ID
  'TenantAdmin',                           -- Role
  'Test',                                  -- First name
  'User',                                  -- Last name
  'active'                                 -- Status
);
```

### 4.3 Verify JWT Claims

1. **Sign in** through your app
2. Check browser console for JWT debug info
3. Or run this query while signed in:

```sql
SELECT auth.jwt();
```

Expected JWT should contain:

```json
{
  "tenant_id": "da8d1551-8af3-4d44-a904-68e7bb2997a3",
  "role": "TenantAdmin",
  "user_profile": {
    "first_name": "Test",
    "last_name": "User"
  }
}
```

## 🧪 Step 4: Test Role-Based Access

### 4.1 Test Data Access

While signed in, run these queries:

```sql
-- Should return your tenant only
SELECT * FROM tenants;

-- Should return empty (no clients created yet)
SELECT * FROM clients;

-- Should return your user record only
SELECT * FROM tenant_users;
```

### 4.2 Test Authentication Functions

In your app, test the enhanced auth functions:

```typescript
import { useSupabaseAuth } from "@strata/auth";

const { getCompleteTenantInfo, hasRole, isTenantAdmin } = useSupabaseAuth();

// Get tenant info (works with or without JWT hooks)
const tenantInfo = await getCompleteTenantInfo();
console.log("Tenant:", tenantInfo?.tenantId);
console.log("Role:", tenantInfo?.role);
console.log("User Profile:", tenantInfo?.userProfile);

// Check roles
console.log("Is Admin:", isTenantAdmin());
console.log("Is Scheduler:", hasRole("Scheduler"));
```

### 4.3 Verify Database Access

Test that RLS is working by querying data:

```typescript
import { DatabaseService } from "@strata/core";
import { supabase } from "@strata/auth";

const db = new DatabaseService(supabase);

// This should only return the user's tenant info
const userTenantInfo = await db.getUserTenantInfo(user.id);
console.log("User Tenant Info:", userTenantInfo);

// This should only return tenants the user has access to
const { data: tenants } = await supabase.from("tenants").select("*");
console.log("Accessible Tenants:", tenants);
```

## 🔄 Step 5: Test Different Roles

### 5.1 Create Inspector User

1. Sign up another user
2. Add them with Inspector role:

```sql
INSERT INTO tenant_users (tenant_id, user_id, role, first_name, last_name, status)
VALUES (
  'da8d1551-8af3-4d44-a904-68e7bb2997a3',
  'INSPECTOR_USER_ID',
  'Inspector',
  'Inspector',
  'User',
  'active'
);
```

### 5.2 Test Role Restrictions

- **TenantAdmin**: Can access all tenant data
- **Scheduler**: Can manage clients, properties, sites, inspections
- **Inspector**: Can only access assigned inspections
- **ClientUser**: Limited read access (to be implemented)

## 🚨 Troubleshooting

### JWT Hook Not Working

**Symptoms**: JWT doesn't contain tenant_id/role claims

**Solutions**:

1. Verify hook is enabled in Authentication > Hooks
2. Check SQL function was created successfully
3. Ensure user exists in tenant_users table
4. Try signing out and back in

### RLS Not Enforced

**Symptoms**: Can access data from other tenants

**Solutions**:

1. Re-run `database/rls-only-setup.sql`
2. Verify RLS is enabled: `SELECT * FROM pg_tables WHERE rowsecurity = true;`
3. Check policies exist: `SELECT * FROM pg_policies;`

### User Can't Access Data

**Symptoms**: All queries return empty or permission denied

**Solutions**:

1. Verify user exists in tenant_users table
2. Check user status is 'active'
3. Ensure JWT contains correct tenant_id
4. Verify user is signed in

## 📊 Verification Checklist

- [ ] JWT hook function created and enabled
- [ ] RLS enabled on all tables
- [ ] RLS policies created and working
- [ ] Test user can sign up and sign in
- [ ] JWT contains tenant_id and role claims
- [ ] User can access only their tenant data
- [ ] Role-based permissions work correctly
- [ ] Different roles have appropriate access levels

## 🎉 Success Criteria

When authentication is properly configured:

1. **Anonymous users** cannot access any tenant data
2. **Authenticated users** can only see their tenant's data
3. **JWT tokens** contain tenant_id and role claims
4. **Role permissions** are enforced (admins vs inspectors)
5. **Multi-tenancy** is working (users can't see other tenants)

## 📚 Next Steps

After authentication is working:

1. **Create sample clients and properties** for testing
2. **Implement user invitation flow** for adding team members
3. **Add client portal access** for ClientUser role
4. **Test mobile and desktop apps** with authentication
5. **Set up user profile management** features

## 🔗 Related Files

- `database/rls-only-setup.sql` - RLS policies and helper functions
- `scripts/test-rls-simple.js` - RLS testing script
- `packages/auth/src/useSupabaseAuth.ts` - Enhanced auth hook
- `packages/core/src/services/database.ts` - User management functions
- `database/SETUP.md` - Database setup guide
- `docs/database-implementation-summary.md` - Schema overview
